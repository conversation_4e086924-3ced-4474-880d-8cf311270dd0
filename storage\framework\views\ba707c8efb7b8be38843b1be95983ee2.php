<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Barcode Scanner')); ?>

            </h2>
            <a href="<?php echo e(route('barcodes.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Barcodes
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Manual Barcode Input -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Manual Barcode Entry</h3>
                    <div class="flex space-x-4">
                        <div class="flex-1">
                            <input type="text" id="barcodeInput" placeholder="Enter or scan barcode here..." 
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                   autofocus>
                        </div>
                        <button id="searchButton" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Search
                        </button>
                        <button id="clearButton" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Clear
                        </button>
                    </div>
                    <p class="mt-2 text-sm text-gray-600">
                        Focus on the input field above and scan a barcode, or type it manually and click Search.
                    </p>
                </div>
            </div>

            <!-- Camera Scanner (Future Enhancement) -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Camera Scanner</h3>
                    <div class="text-center py-8 bg-gray-50 rounded-lg">
                        <div class="text-gray-500 mb-4">
                            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <p class="text-gray-600">Camera-based barcode scanning coming soon!</p>
                        <p class="text-sm text-gray-500 mt-2">For now, use a USB barcode scanner or manual entry above.</p>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="hidden">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Product Found</h3>
                        <div id="productDetails">
                            <!-- Product details will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="hidden">
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <div class="flex">
                        <div class="py-1">
                            <svg class="fill-current h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-bold">Product Not Found</p>
                            <p class="text-sm" id="errorText">No product found with this barcode.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Scans -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Scans</h3>
                    <div id="recentScans" class="space-y-2">
                        <p class="text-gray-500 text-sm">No recent scans</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const barcodeInput = document.getElementById('barcodeInput');
            const searchButton = document.getElementById('searchButton');
            const clearButton = document.getElementById('clearButton');
            const searchResults = document.getElementById('searchResults');
            const errorMessage = document.getElementById('errorMessage');
            const productDetails = document.getElementById('productDetails');
            const errorText = document.getElementById('errorText');
            const recentScans = document.getElementById('recentScans');

            let recentScansData = JSON.parse(localStorage.getItem('recentScans') || '[]');

            // Search functionality
            function searchProduct() {
                const barcode = barcodeInput.value.trim();
                
                if (!barcode) {
                    alert('Please enter a barcode');
                    return;
                }

                // Hide previous results
                searchResults.classList.add('hidden');
                errorMessage.classList.add('hidden');

                // Make API call
                fetch('<?php echo e(route("barcodes.scan")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({ barcode: barcode })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayProduct(data.product);
                        addToRecentScans(data.product);
                    } else {
                        showError(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('An error occurred while searching for the product.');
                });
            }

            function displayProduct(product) {
                productDetails.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            ${product.image_url ? `<img src="${product.image_url}" alt="${product.name}" class="w-full h-48 object-cover rounded-lg mb-4">` : ''}
                            <h4 class="text-xl font-semibold text-gray-900 mb-2">${product.name}</h4>
                            <p class="text-gray-600 mb-4">${product.category}</p>
                            
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Barcode</dt>
                                    <dd class="text-sm font-mono text-gray-900">${product.barcode}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Metal Type</dt>
                                    <dd class="text-sm text-gray-900">${product.metal_type} ${product.purity}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Weight</dt>
                                    <dd class="text-sm text-gray-900">${product.net_weight}g</dd>
                                </div>
                                ${product.huid_number ? `
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">HUID Number</dt>
                                    <dd class="text-sm text-blue-600">${product.huid_number}</dd>
                                </div>
                                ` : ''}
                            </dl>
                        </div>
                        
                        <div>
                            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                                <h5 class="text-lg font-semibold text-gray-900 mb-2">Pricing</h5>
                                <div class="text-3xl font-bold text-green-600">₹${parseFloat(product.selling_price).toLocaleString()}</div>
                            </div>
                            
                            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                                <h5 class="text-lg font-semibold text-gray-900 mb-2">Stock</h5>
                                <div class="text-2xl font-bold ${product.quantity > 0 ? 'text-green-600' : 'text-red-600'}">
                                    ${product.quantity} ${product.quantity === 1 ? 'piece' : 'pieces'}
                                </div>
                                <div class="text-sm text-gray-500 mt-1">
                                    Status: <span class="font-medium">${product.status}</span>
                                </div>
                            </div>
                            
                            <div class="flex space-x-2">
                                <a href="/products/${product.id}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex-1 text-center">
                                    View Details
                                </a>
                                <a href="/sales/create?product_id=${product.id}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex-1 text-center">
                                    Add to Sale
                                </a>
                            </div>
                        </div>
                    </div>
                `;
                searchResults.classList.remove('hidden');
            }

            function showError(message) {
                errorText.textContent = message;
                errorMessage.classList.remove('hidden');
            }

            function addToRecentScans(product) {
                // Add to beginning of array
                recentScansData.unshift({
                    ...product,
                    scannedAt: new Date().toISOString()
                });

                // Keep only last 10 scans
                recentScansData = recentScansData.slice(0, 10);

                // Save to localStorage
                localStorage.setItem('recentScans', JSON.stringify(recentScansData));

                // Update display
                updateRecentScansDisplay();
            }

            function updateRecentScansDisplay() {
                if (recentScansData.length === 0) {
                    recentScans.innerHTML = '<p class="text-gray-500 text-sm">No recent scans</p>';
                    return;
                }

                recentScans.innerHTML = recentScansData.map(scan => `
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900">${scan.name}</div>
                            <div class="text-sm text-gray-500">${scan.barcode} • ${new Date(scan.scannedAt).toLocaleString()}</div>
                        </div>
                        <div class="text-right">
                            <div class="font-medium text-gray-900">₹${parseFloat(scan.selling_price).toLocaleString()}</div>
                            <div class="text-sm text-gray-500">${scan.quantity} in stock</div>
                        </div>
                    </div>
                `).join('');
            }

            // Event listeners
            searchButton.addEventListener('click', searchProduct);
            
            clearButton.addEventListener('click', function() {
                barcodeInput.value = '';
                searchResults.classList.add('hidden');
                errorMessage.classList.add('hidden');
                barcodeInput.focus();
            });

            barcodeInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchProduct();
                }
            });

            // Auto-focus on barcode input
            barcodeInput.focus();

            // Initialize recent scans display
            updateRecentScansDisplay();

            // Clear input after a delay (for barcode scanners)
            let clearTimeout;
            barcodeInput.addEventListener('input', function() {
                clearTimeout(clearTimeout);
                clearTimeout = setTimeout(() => {
                    if (this.value.length >= 8) { // Minimum barcode length
                        searchProduct();
                    }
                }, 100);
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\barcodes\scanner.blade.php ENDPATH**/ ?>