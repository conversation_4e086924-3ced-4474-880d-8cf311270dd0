<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Barcode Management')); ?>

            </h2>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('barcodes.scanner')); ?>" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Barcode Scanner
                </a>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create_product')): ?>
                    <form method="POST" action="<?php echo e(route('barcodes.generate-missing')); ?>" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                onclick="return confirm('Generate barcodes for all products without barcodes?')">
                            Generate Missing Barcodes
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="<?php echo e(route('barcodes.index')); ?>" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                                   placeholder="Search by name, barcode, HUID..." 
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <select name="category" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Categories</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category); ?>" <?php echo e(request('category') === $category ? 'selected' : ''); ?>><?php echo e($category); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div>
                            <select name="metal_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Metals</option>
                                <option value="Gold" <?php echo e(request('metal_type') === 'Gold' ? 'selected' : ''); ?>>Gold</option>
                                <option value="Silver" <?php echo e(request('metal_type') === 'Silver' ? 'selected' : ''); ?>>Silver</option>
                                <option value="Platinum" <?php echo e(request('metal_type') === 'Platinum' ? 'selected' : ''); ?>>Platinum</option>
                            </select>
                        </div>
                        <div>
                            <select name="barcode_status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Products</option>
                                <option value="with_barcode" <?php echo e(request('barcode_status') === 'with_barcode' ? 'selected' : ''); ?>>With Barcode</option>
                                <option value="without_barcode" <?php echo e(request('barcode_status') === 'without_barcode' ? 'selected' : ''); ?>>Without Barcode</option>
                            </select>
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Search
                            </button>
                            <a href="<?php echo e(route('barcodes.index')); ?>" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Actions</h3>
                    <form id="bulkActionForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="bulk_count" class="block text-sm font-medium text-gray-700">Labels per Product</label>
                            <input type="number" id="bulk_count" name="count" value="1" min="1" max="100"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <label for="label_size" class="block text-sm font-medium text-gray-700">Label Size</label>
                            <select id="label_size" name="label_size"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="small">Small (2x1 inch)</option>
                                <option value="medium" selected>Medium (3x2 inch)</option>
                                <option value="large">Large (4x3 inch)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Include on Label</label>
                            <div class="space-y-1">
                                <label class="flex items-center">
                                    <input type="checkbox" name="include_price" value="1" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">Price</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="include_weight" value="1" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">Weight</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="include_huid" value="1" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">HUID</span>
                                </label>
                            </div>
                        </div>
                        <div class="flex items-end">
                            <button type="button" id="printSelectedLabels" class="w-full bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                Print Selected Labels
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" id="selectAll" class="rounded border-gray-300">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Barcode</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" name="selected_products[]" value="<?php echo e($product->id); ?>" class="product-checkbox rounded border-gray-300">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <?php if($product->image_path): ?>
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <img class="h-10 w-10 rounded-full object-cover" src="<?php echo e(asset('storage/' . $product->image_path)); ?>" alt="<?php echo e($product->name); ?>">
                                                    </div>
                                                <?php endif; ?>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900"><?php echo e($product->name); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo e($product->category); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if($product->barcode): ?>
                                                <div class="text-sm font-mono text-gray-900"><?php echo e($product->barcode); ?></div>
                                                <div class="mt-1">
                                                    <img src="<?php echo e(route('barcodes.generate', ['product' => $product, 'format' => 'png'])); ?>" 
                                                         alt="Barcode" class="h-8">
                                                </div>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    No Barcode
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo e($product->metal_type); ?> <?php echo e($product->purity); ?></div>
                                            <div class="text-sm text-gray-500">Weight: <?php echo e($product->net_weight); ?>g</div>
                                            <?php if($product->huid_number): ?>
                                                <div class="text-xs text-blue-600">HUID: <?php echo e($product->huid_number); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">₹<?php echo e(number_format($product->selling_price, 2)); ?></div>
                                            <div class="text-sm text-gray-500">Stock: <?php echo e($product->quantity); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <?php if($product->barcode): ?>
                                                    <a href="<?php echo e(route('barcodes.generate', ['product' => $product, 'format' => 'png'])); ?>" 
                                                       target="_blank" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                    <a href="<?php echo e(route('barcodes.generate', ['product' => $product, 'format' => 'svg'])); ?>" 
                                                       target="_blank" class="text-green-600 hover:text-green-900">SVG</a>
                                                <?php else: ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create_product')): ?>
                                                        <form method="POST" action="<?php echo e(route('barcodes.generate-new', $product)); ?>" class="inline">
                                                            <?php echo csrf_field(); ?>
                                                            <button type="submit" class="text-blue-600 hover:text-blue-900">Generate</button>
                                                        </form>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                <a href="<?php echo e(route('products.show', $product)); ?>" class="text-gray-600 hover:text-gray-900">Details</a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            No products found.
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        <?php echo e($products->links()); ?>

                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <?php if($products->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Products</div>
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($products->total()); ?></div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">With Barcodes</div>
                            <div class="text-2xl font-bold text-green-600"><?php echo e($products->where('barcode', '!=', null)->count()); ?></div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Without Barcodes</div>
                            <div class="text-2xl font-bold text-red-600"><?php echo e($products->where('barcode', null)->count()); ?></div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Value</div>
                            <div class="text-2xl font-bold text-gray-900">₹<?php echo e(number_format($products->sum('selling_price'), 2)); ?></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const productCheckboxes = document.querySelectorAll('.product-checkbox');
            const printSelectedButton = document.getElementById('printSelectedLabels');

            // Select all functionality
            selectAllCheckbox.addEventListener('change', function() {
                productCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // Print selected labels
            printSelectedButton.addEventListener('click', function() {
                const selectedProducts = Array.from(productCheckboxes)
                    .filter(checkbox => checkbox.checked)
                    .map(checkbox => checkbox.value);

                if (selectedProducts.length === 0) {
                    alert('Please select at least one product');
                    return;
                }

                const form = document.getElementById('bulkActionForm');
                const formData = new FormData(form);
                formData.append('_token', '<?php echo e(csrf_token()); ?>');
                
                selectedProducts.forEach(productId => {
                    formData.append('product_ids[]', productId);
                });

                // Create a temporary form and submit
                const tempForm = document.createElement('form');
                tempForm.method = 'POST';
                tempForm.action = '<?php echo e(route("barcodes.print-labels")); ?>';
                tempForm.style.display = 'none';

                for (let [key, value] of formData.entries()) {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = value;
                    tempForm.appendChild(input);
                }

                document.body.appendChild(tempForm);
                tempForm.submit();
                document.body.removeChild(tempForm);
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\barcodes\index.blade.php ENDPATH**/ ?>