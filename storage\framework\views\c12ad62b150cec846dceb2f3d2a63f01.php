<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Sale Details')); ?> - <?php echo e($sale->invoice_number); ?>

            </h2>
            <div class="flex space-x-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('print_invoice')): ?>
                    <a href="<?php echo e(route('sales.print', $sale)); ?>" target="_blank" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Print Invoice
                    </a>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export_invoice')): ?>
                    <a href="<?php echo e(route('sales.invoice', $sale)); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Download PDF
                    </a>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_sale')): ?>
                    <a href="<?php echo e(route('sales.edit', $sale)); ?>" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                        Edit Sale
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('sales.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Sales
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Sale Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Sale Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Sale Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Invoice Number</dt>
                                    <dd class="text-sm text-gray-900 font-mono"><?php echo e($sale->invoice_number); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Sale Date</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($sale->sale_date->format('d M, Y')); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created By</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($sale->createdBy->name); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created At</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($sale->created_at->format('d M, Y h:i A')); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Payment Status</dt>
                                    <dd class="text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php echo e($sale->payment_status === 'paid' ? 'bg-green-100 text-green-800' : 
                                               ($sale->payment_status === 'partial' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')); ?>">
                                            <?php echo e(ucfirst($sale->payment_status)); ?>

                                        </span>
                                    </dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Customer Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($sale->customer->name); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Mobile</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($sale->customer->mobile); ?></dd>
                                </div>
                                <?php if($sale->customer->email): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="text-sm text-gray-900"><?php echo e($sale->customer->email); ?></dd>
                                    </div>
                                <?php endif; ?>
                                <?php if($sale->customer->address): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="text-sm text-gray-900"><?php echo e($sale->customer->full_address); ?></dd>
                                    </div>
                                <?php endif; ?>
                            </dl>
                        </div>
                    </div>

                    <?php if($sale->notes): ?>
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                            <p class="text-sm text-gray-700"><?php echo e($sale->notes); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sale Items -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Sale Items</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metal Rate</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Making</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stone</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wastage</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__currentLoopData = $sale->saleItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <?php if($item->product->image_path): ?>
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <img class="h-10 w-10 rounded-full object-cover" src="<?php echo e(asset('storage/' . $item->product->image_path)); ?>" alt="<?php echo e($item->product->name); ?>">
                                                    </div>
                                                <?php endif; ?>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900"><?php echo e($item->product->name); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo e($item->product->barcode); ?></div>
                                                    <div class="text-xs text-gray-500"><?php echo e($item->product->metal_type); ?> <?php echo e($item->product->purity); ?> - <?php echo e($item->product->net_weight); ?>g</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo e($item->quantity); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹<?php echo e(number_format($item->metal_rate, 2)); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹<?php echo e(number_format($item->making_charges, 2)); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹<?php echo e(number_format($item->stone_charges, 2)); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹<?php echo e(number_format($item->wastage_amount, 2)); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">₹<?php echo e(number_format($item->item_total, 2)); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Billing Summary and Payment Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Billing Summary -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Billing Summary</h3>
                        <dl class="space-y-2">
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500">Subtotal</dt>
                                <dd class="text-sm text-gray-900">₹<?php echo e(number_format($sale->subtotal, 2)); ?></dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500">CGST (1.5%)</dt>
                                <dd class="text-sm text-gray-900">₹<?php echo e(number_format($sale->cgst_amount, 2)); ?></dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500">SGST (1.5%)</dt>
                                <dd class="text-sm text-gray-900">₹<?php echo e(number_format($sale->sgst_amount, 2)); ?></dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500">Total Tax</dt>
                                <dd class="text-sm text-gray-900">₹<?php echo e(number_format($sale->total_tax, 2)); ?></dd>
                            </div>
                            <?php if($sale->discount_amount > 0): ?>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Discount</dt>
                                    <dd class="text-sm text-green-600">-₹<?php echo e(number_format($sale->discount_amount, 2)); ?></dd>
                                </div>
                            <?php endif; ?>
                            <div class="flex justify-between border-t pt-2">
                                <dt class="text-base font-medium text-gray-900">Total Amount</dt>
                                <dd class="text-base font-bold text-gray-900">₹<?php echo e(number_format($sale->total_amount, 2)); ?></dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Payment Details -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Details</h3>
                        <dl class="space-y-2">
                            <?php if($sale->cash_payment > 0): ?>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Cash Payment</dt>
                                    <dd class="text-sm text-gray-900">₹<?php echo e(number_format($sale->cash_payment, 2)); ?></dd>
                                </div>
                            <?php endif; ?>
                            <?php if($sale->card_payment > 0): ?>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Card Payment</dt>
                                    <dd class="text-sm text-gray-900">₹<?php echo e(number_format($sale->card_payment, 2)); ?></dd>
                                </div>
                            <?php endif; ?>
                            <?php if($sale->upi_payment > 0): ?>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">UPI Payment</dt>
                                    <dd class="text-sm text-gray-900">₹<?php echo e(number_format($sale->upi_payment, 2)); ?></dd>
                                </div>
                            <?php endif; ?>
                            <?php if($sale->old_gold_adjustment > 0): ?>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Old Gold Adjustment</dt>
                                    <dd class="text-sm text-gray-900">₹<?php echo e(number_format($sale->old_gold_adjustment, 2)); ?></dd>
                                </div>
                            <?php endif; ?>
                            <div class="flex justify-between border-t pt-2">
                                <dt class="text-base font-medium text-gray-900">Total Payment</dt>
                                <dd class="text-base font-bold text-green-600">₹<?php echo e(number_format($sale->total_payment, 2)); ?></dd>
                            </div>
                            <?php if($sale->balance_amount > 0): ?>
                                <div class="flex justify-between">
                                    <dt class="text-base font-medium text-gray-900">Balance Amount</dt>
                                    <dd class="text-base font-bold text-red-600">₹<?php echo e(number_format($sale->balance_amount, 2)); ?></dd>
                                </div>
                            <?php endif; ?>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views/sales/show.blade.php ENDPATH**/ ?>