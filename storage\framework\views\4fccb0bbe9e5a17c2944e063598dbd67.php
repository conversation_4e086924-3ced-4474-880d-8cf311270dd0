<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Repair Receipt - <?php echo e($repair->repair_number); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .company-details {
            font-size: 11px;
            color: #666;
        }
        .receipt-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            color: #333;
        }
        .details-section {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .details-left, .details-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 10px;
        }
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .info-box {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-received { background: #e3f2fd; color: #1976d2; }
        .status-in-progress { background: #fff3e0; color: #f57c00; }
        .status-completed { background: #e8f5e8; color: #388e3c; }
        .status-delivered { background: #f3e5f5; color: #7b1fa2; }
        .status-cancelled { background: #ffebee; color: #d32f2f; }
        .repair-details {
            margin: 20px 0;
        }
        .repair-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .repair-details td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .repair-details .label {
            font-weight: bold;
            width: 30%;
            color: #333;
        }
        .cost-summary {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .cost-summary table {
            width: 100%;
        }
        .cost-summary td {
            padding: 5px 0;
        }
        .total-row {
            font-weight: bold;
            font-size: 14px;
            border-top: 2px solid #333;
            padding-top: 10px;
        }
        .terms {
            margin-top: 30px;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        .signature {
            margin-top: 40px;
            display: table;
            width: 100%;
        }
        .signature-left, .signature-right {
            display: table-cell;
            width: 50%;
            text-align: center;
            vertical-align: bottom;
        }
        .signature-line {
            border-top: 1px solid #333;
            width: 150px;
            margin: 0 auto 10px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name"><?php echo e(config('app.name', 'Jewel Pro')); ?></div>
        <div class="company-details">
            Premium Jewelry Store<br>
            123 Main Street, City, State - 123456<br>
            Phone: +91 98765 43210 | Email: <EMAIL><br>
            GSTIN: 29ABCDE1234F1Z5
        </div>
    </div>

    <div class="receipt-title">REPAIR SERVICE RECEIPT</div>

    <div class="details-section">
        <div class="details-left">
            <div class="section-title">Customer Information</div>
            <div class="info-box">
                <strong><?php echo e($repair->customer->name); ?></strong><br>
                Mobile: <?php echo e($repair->customer->mobile); ?><br>
                <?php if($repair->customer->email): ?>
                    Email: <?php echo e($repair->customer->email); ?><br>
                <?php endif; ?>
                <?php if($repair->customer->address): ?>
                    Address: <?php echo e($repair->customer->address); ?>

                <?php endif; ?>
            </div>
        </div>
        <div class="details-right">
            <div class="section-title">Repair Information</div>
            <div class="info-box">
                <strong>Receipt #:</strong> <?php echo e($repair->repair_number); ?><br>
                <strong>Received Date:</strong> <?php echo e($repair->received_date->format('d/m/Y')); ?><br>
                <strong>Status:</strong> 
                <span class="status-badge status-<?php echo e($repair->status); ?>"><?php echo e(ucfirst(str_replace('_', ' ', $repair->status))); ?></span><br>
                <?php if($repair->expected_completion_date): ?>
                    <strong>Expected Completion:</strong> <?php echo e($repair->expected_completion_date->format('d/m/Y')); ?>

                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="repair-details">
        <div class="section-title">Repair Details</div>
        <table>
            <tr>
                <td class="label">Item Description:</td>
                <td><?php echo e($repair->item_description); ?></td>
            </tr>
            <tr>
                <td class="label">Repair Type:</td>
                <td><?php echo e(ucfirst(str_replace('_', ' ', $repair->repair_type))); ?></td>
            </tr>
            <tr>
                <td class="label">Problem Description:</td>
                <td><?php echo e($repair->problem_description); ?></td>
            </tr>
            <?php if($repair->work_done): ?>
            <tr>
                <td class="label">Work Done:</td>
                <td><?php echo e($repair->work_done); ?></td>
            </tr>
            <?php endif; ?>
            <?php if($repair->notes): ?>
            <tr>
                <td class="label">Notes:</td>
                <td><?php echo e($repair->notes); ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>

    <?php if($repair->estimated_cost || $repair->actual_cost): ?>
    <div class="cost-summary">
        <div class="section-title">Cost Summary</div>
        <table>
            <?php if($repair->estimated_cost): ?>
            <tr>
                <td>Estimated Cost:</td>
                <td style="text-align: right;">₹<?php echo e(number_format($repair->estimated_cost, 2)); ?></td>
            </tr>
            <?php endif; ?>
            <?php if($repair->actual_cost): ?>
            <tr>
                <td>Actual Cost:</td>
                <td style="text-align: right;">₹<?php echo e(number_format($repair->actual_cost, 2)); ?></td>
            </tr>
            <?php endif; ?>
            <?php if($repair->amount_paid): ?>
            <tr>
                <td>Amount Paid:</td>
                <td style="text-align: right;">₹<?php echo e(number_format($repair->amount_paid, 2)); ?></td>
            </tr>
            <?php endif; ?>
            <?php if($repair->actual_cost && $repair->amount_paid): ?>
            <tr class="total-row">
                <td><strong>Balance Due:</strong></td>
                <td style="text-align: right;"><strong>₹<?php echo e(number_format($repair->actual_cost - $repair->amount_paid, 2)); ?></strong></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
    <?php endif; ?>

    <div class="terms">
        <strong>Terms & Conditions:</strong><br>
        1. This receipt must be presented at the time of item collection.<br>
        2. Items not collected within 30 days of completion will incur storage charges.<br>
        3. We are not responsible for any damage to items left beyond 60 days.<br>
        4. All repair work is guaranteed for 30 days from completion date.<br>
        5. Payment must be made in full before item collection.
    </div>

    <div class="signature">
        <div class="signature-left">
            <div class="signature-line"></div>
            <strong>Customer Signature</strong>
        </div>
        <div class="signature-right">
            <div class="signature-line"></div>
            <strong>Authorized Signatory</strong>
        </div>
    </div>

    <div class="footer">
        Thank you for choosing our repair services!<br>
        For any queries, please contact us at +91 98765 43210
    </div>
</body>
</html>
<?php /**PATH C:\proj\jewel-pro\resources\views\repairs\receipt.blade.php ENDPATH**/ ?>