<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            color: #333;
        }
        .report-period {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        .summary-cards {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .summary-card {
            display: table-cell;
            width: 25%;
            padding: 15px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            text-align: center;
        }
        .summary-card .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .summary-card .label {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .data-table .text-right {
            text-align: right;
        }
        .data-table .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name"><?php echo e(config('app.name', 'Jewel Pro')); ?></div>
        <div class="report-title">Sales Report</div>
        <div class="report-period">
            Period: <?php echo e($fromDate ? date('d/m/Y', strtotime($fromDate)) : 'All Time'); ?> 
            <?php if($fromDate && $toDate): ?> - <?php echo e(date('d/m/Y', strtotime($toDate))); ?> <?php endif; ?>
        </div>
    </div>

    <div class="summary-cards">
        <div class="summary-card">
            <div class="value"><?php echo e($data['total_sales'] ?? 0); ?></div>
            <div class="label">Total Sales</div>
        </div>
        <div class="summary-card">
            <div class="value">₹<?php echo e(number_format($data['total_revenue'] ?? 0, 2)); ?></div>
            <div class="label">Total Revenue</div>
        </div>
        <div class="summary-card">
            <div class="value">₹<?php echo e(number_format($data['average_sale'] ?? 0, 2)); ?></div>
            <div class="label">Average Sale</div>
        </div>
        <div class="summary-card">
            <div class="value"><?php echo e($data['total_customers'] ?? 0); ?></div>
            <div class="label">Customers</div>
        </div>
    </div>

    <?php if(isset($data['sales']) && count($data['sales']) > 0): ?>
    <table class="data-table">
        <thead>
            <tr>
                <th>Date</th>
                <th>Invoice #</th>
                <th>Customer</th>
                <th>Items</th>
                <th class="text-right">Amount</th>
                <th class="text-center">Payment</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $data['sales']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($sale->sale_date->format('d/m/Y')); ?></td>
                <td><?php echo e($sale->invoice_number); ?></td>
                <td><?php echo e($sale->customer->name); ?></td>
                <td><?php echo e($sale->saleItems->count()); ?></td>
                <td class="text-right">₹<?php echo e(number_format($sale->total_amount, 2)); ?></td>
                <td class="text-center"><?php echo e(ucfirst($sale->payment_method)); ?></td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    <?php endif; ?>

    <?php if(isset($data['top_customers']) && count($data['top_customers']) > 0): ?>
    <h3>Top Customers</h3>
    <table class="data-table">
        <thead>
            <tr>
                <th>Customer Name</th>
                <th class="text-center">Total Orders</th>
                <th class="text-right">Total Amount</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $data['top_customers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($customer->name); ?></td>
                <td class="text-center"><?php echo e($customer->sales_count); ?></td>
                <td class="text-right">₹<?php echo e(number_format($customer->total_amount, 2)); ?></td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    <?php endif; ?>

    <div class="footer">
        Report generated on <?php echo e(date('d/m/Y H:i:s')); ?><br>
        <?php echo e(config('app.name', 'Jewel Pro')); ?> - Sales Management System
    </div>
</body>
</html>
<?php /**PATH C:\proj\jewel-pro\resources\views\reports\pdf\sales.blade.php ENDPATH**/ ?>