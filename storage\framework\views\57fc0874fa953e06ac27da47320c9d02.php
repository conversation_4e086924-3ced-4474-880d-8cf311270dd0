<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Edit Old Gold Purchase')); ?> - #<?php echo e($oldGoldPurchase->purchase_number); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="<?php echo e(route('old-gold-purchases.update', $oldGoldPurchase)); ?>" id="purchaseForm">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <!-- Customer Selection -->
                            <div>
                                <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer</label>
                                <select name="customer_id" id="customer_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="">Select Customer</option>
                                    <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($customer->id); ?>" <?php echo e($oldGoldPurchase->customer_id == $customer->id ? 'selected' : ''); ?>>
                                            <?php echo e($customer->name); ?> - <?php echo e($customer->mobile); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['customer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Purchase Date -->
                            <div>
                                <label for="purchase_date" class="block text-sm font-medium text-gray-700">Purchase Date</label>
                                <input type="date" name="purchase_date" id="purchase_date" value="<?php echo e($oldGoldPurchase->purchase_date->format('Y-m-d')); ?>" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <?php $__errorArgs = ['purchase_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Payment Method -->
                            <div>
                                <label for="payment_method" class="block text-sm font-medium text-gray-700">Payment Method</label>
                                <select name="payment_method" id="payment_method" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="">Select Payment Method</option>
                                    <option value="cash" <?php echo e($oldGoldPurchase->payment_method == 'cash' ? 'selected' : ''); ?>>Cash</option>
                                    <option value="bank_transfer" <?php echo e($oldGoldPurchase->payment_method == 'bank_transfer' ? 'selected' : ''); ?>>Bank Transfer</option>
                                    <option value="cheque" <?php echo e($oldGoldPurchase->payment_method == 'cheque' ? 'selected' : ''); ?>>Cheque</option>
                                    <option value="exchange" <?php echo e($oldGoldPurchase->payment_method == 'exchange' ? 'selected' : ''); ?>>Exchange</option>
                                </select>
                                <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Payment Status -->
                            <div>
                                <label for="payment_status" class="block text-sm font-medium text-gray-700">Payment Status</label>
                                <select name="payment_status" id="payment_status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="pending" <?php echo e($oldGoldPurchase->payment_status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                    <option value="partial" <?php echo e($oldGoldPurchase->payment_status == 'partial' ? 'selected' : ''); ?>>Partial</option>
                                    <option value="paid" <?php echo e($oldGoldPurchase->payment_status == 'paid' ? 'selected' : ''); ?>>Paid</option>
                                </select>
                                <?php $__errorArgs = ['payment_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Purchase Items -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Purchase Items</h3>
                                <button type="button" id="addItem" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Add Item
                                </button>
                            </div>

                            <div id="itemsContainer">
                                <?php $__currentLoopData = $oldGoldPurchase->purchaseItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="item-row border border-gray-200 rounded-lg p-4 mb-4">
                                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700">Item Description</label>
                                            <input type="text" name="items[<?php echo e($index); ?>][item_description]" value="<?php echo e($item->item_description); ?>" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Metal Type</label>
                                            <select name="items[<?php echo e($index); ?>][metal_type]" class="metal-type mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                <option value="">Select</option>
                                                <option value="Gold" <?php echo e($item->metal_type == 'Gold' ? 'selected' : ''); ?>>Gold</option>
                                                <option value="Silver" <?php echo e($item->metal_type == 'Silver' ? 'selected' : ''); ?>>Silver</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Purity</label>
                                            <select name="items[<?php echo e($index); ?>][purity]" class="purity mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                <option value="">Select</option>
                                                <option value="22K" <?php echo e($item->purity == '22K' ? 'selected' : ''); ?>>22K</option>
                                                <option value="18K" <?php echo e($item->purity == '18K' ? 'selected' : ''); ?>>18K</option>
                                                <option value="916" <?php echo e($item->purity == '916' ? 'selected' : ''); ?>>916</option>
                                                <option value="999" <?php echo e($item->purity == '999' ? 'selected' : ''); ?>>999</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Gross Weight (g)</label>
                                            <input type="number" name="items[<?php echo e($index); ?>][gross_weight]" value="<?php echo e($item->gross_weight); ?>" step="0.001" class="gross-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Net Weight (g)</label>
                                            <input type="number" name="items[<?php echo e($index); ?>][net_weight]" value="<?php echo e($item->net_weight); ?>" step="0.001" class="net-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                    </div>
                                    <div class="mt-4 flex justify-between items-center">
                                        <div class="text-sm text-gray-600">
                                            Rate: ₹<span class="rate-display"><?php echo e(number_format($item->rate_per_gram, 2)); ?></span>/g | 
                                            Amount: ₹<span class="amount-display"><?php echo e(number_format($item->amount, 2)); ?></span>
                                        </div>
                                        <button type="button" class="remove-item bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
                                            Remove
                                        </button>
                                    </div>
                                    <input type="hidden" name="items[<?php echo e($index); ?>][rate_per_gram]" value="<?php echo e($item->rate_per_gram); ?>" class="rate-input">
                                    <input type="hidden" name="items[<?php echo e($index); ?>][amount]" value="<?php echo e($item->amount); ?>" class="amount-input">
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"><?php echo e($oldGoldPurchase->notes); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Totals -->
                        <div class="border-t pt-6 mb-6">
                            <div class="flex justify-end">
                                <div class="w-64">
                                    <dl class="space-y-2">
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Total Weight</dt>
                                            <dd class="text-sm text-gray-900"><span id="totalWeight"><?php echo e(number_format($oldGoldPurchase->total_weight, 3)); ?></span>g</dd>
                                        </div>
                                        <div class="flex justify-between border-t pt-2">
                                            <dt class="text-base font-medium text-gray-900">Total Amount</dt>
                                            <dd class="text-base font-medium text-gray-900">₹<span id="totalAmount"><?php echo e(number_format($oldGoldPurchase->total_amount, 2)); ?></span></dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Amount Paid</dt>
                                            <dd class="text-sm text-gray-900">₹<span id="amountPaid"><?php echo e(number_format($oldGoldPurchase->amount_paid, 2)); ?></span></dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Balance</dt>
                                            <dd class="text-sm text-gray-900">₹<span id="balance"><?php echo e(number_format($oldGoldPurchase->total_amount - $oldGoldPurchase->amount_paid, 2)); ?></span></dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-4">
                            <a href="<?php echo e(route('old-gold-purchases.show', $oldGoldPurchase)); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Purchase
                            </button>
                        </div>

                        <input type="hidden" name="total_weight" id="totalWeightInput" value="<?php echo e($oldGoldPurchase->total_weight); ?>">
                        <input type="hidden" name="total_amount" id="totalAmountInput" value="<?php echo e($oldGoldPurchase->total_amount); ?>">
                        <input type="hidden" name="amount_paid" id="amountPaidInput" value="<?php echo e($oldGoldPurchase->amount_paid); ?>">
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Metal rates data
        const metalRates = <?php echo json_encode($metalRates->keyBy(function($rate) { return $rate->metal_type . '-' . $rate->purity; }), 15, 512) ?>;
        let itemIndex = <?php echo e(count($oldGoldPurchase->purchaseItems)); ?>;

        // Add item functionality
        document.getElementById('addItem').addEventListener('click', function() {
            const container = document.getElementById('itemsContainer');
            const itemHtml = `
                <div class="item-row border border-gray-200 rounded-lg p-4 mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Item Description</label>
                            <input type="text" name="items[${itemIndex}][item_description]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Metal Type</label>
                            <select name="items[${itemIndex}][metal_type]" class="metal-type mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <option value="">Select</option>
                                <option value="Gold">Gold</option>
                                <option value="Silver">Silver</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Purity</label>
                            <select name="items[${itemIndex}][purity]" class="purity mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <option value="">Select</option>
                                <option value="22K">22K</option>
                                <option value="18K">18K</option>
                                <option value="916">916</option>
                                <option value="999">999</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Gross Weight (g)</label>
                            <input type="number" name="items[${itemIndex}][gross_weight]" step="0.001" class="gross-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Net Weight (g)</label>
                            <input type="number" name="items[${itemIndex}][net_weight]" step="0.001" class="net-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            Rate: ₹<span class="rate-display">0.00</span>/g | 
                            Amount: ₹<span class="amount-display">0.00</span>
                        </div>
                        <button type="button" class="remove-item bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
                            Remove
                        </button>
                    </div>
                    <input type="hidden" name="items[${itemIndex}][rate_per_gram]" value="0" class="rate-input">
                    <input type="hidden" name="items[${itemIndex}][amount]" value="0" class="amount-input">
                </div>
            `;
            container.insertAdjacentHTML('beforeend', itemHtml);
            itemIndex++;
            attachEventListeners();
        });

        // Remove item functionality
        function attachEventListeners() {
            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    this.closest('.item-row').remove();
                    calculateTotals();
                });
            });

            // Calculate amounts when inputs change
            document.querySelectorAll('.metal-type, .purity, .net-weight').forEach(input => {
                input.addEventListener('change', function() {
                    calculateItemAmount(this.closest('.item-row'));
                });
            });
        }

        function calculateItemAmount(row) {
            const metalType = row.querySelector('.metal-type').value;
            const purity = row.querySelector('.purity').value;
            const netWeight = parseFloat(row.querySelector('.net-weight').value) || 0;

            if (metalType && purity && netWeight > 0) {
                const rateKey = metalType + '-' + purity;
                const rate = metalRates[rateKey] ? metalRates[rateKey].rate_per_gram : 0;
                const amount = netWeight * rate;

                row.querySelector('.rate-display').textContent = rate.toFixed(2);
                row.querySelector('.amount-display').textContent = amount.toFixed(2);
                row.querySelector('.rate-input').value = rate;
                row.querySelector('.amount-input').value = amount;

                calculateTotals();
            }
        }

        function calculateTotals() {
            let totalWeight = 0;
            let totalAmount = 0;

            document.querySelectorAll('.item-row').forEach(row => {
                const netWeight = parseFloat(row.querySelector('.net-weight').value) || 0;
                const amount = parseFloat(row.querySelector('.amount-input').value) || 0;
                
                totalWeight += netWeight;
                totalAmount += amount;
            });

            document.getElementById('totalWeight').textContent = totalWeight.toFixed(3);
            document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);

            document.getElementById('totalWeightInput').value = totalWeight;
            document.getElementById('totalAmountInput').value = totalAmount;

            // Update balance
            const amountPaid = parseFloat(document.getElementById('amountPaidInput').value) || 0;
            const balance = totalAmount - amountPaid;
            document.getElementById('balance').textContent = balance.toFixed(2);
        }

        // Initialize event listeners
        attachEventListeners();
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\old-gold-purchases\edit.blade.php ENDPATH**/ ?>