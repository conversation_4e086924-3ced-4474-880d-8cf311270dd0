<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Metal Rate') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('metal-rates.show', $metalRate) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Details
                </a>
                <a href="{{ route('metal-rates.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Metal Rates
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('metal-rates.update', $metalRate) }}">
                        @csrf
                        @method('PUT')

                        <!-- Metal Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="metal_type" class="block text-sm font-medium text-gray-700">Metal Type *</label>
                                    <select name="metal_type" id="metal_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Metal Type</option>
                                        <option value="Gold" {{ old('metal_type', $metalRate->metal_type) === 'Gold' ? 'selected' : '' }}>Gold</option>
                                        <option value="Silver" {{ old('metal_type', $metalRate->metal_type) === 'Silver' ? 'selected' : '' }}>Silver</option>
                                        <option value="Platinum" {{ old('metal_type', $metalRate->metal_type) === 'Platinum' ? 'selected' : '' }}>Platinum</option>
                                    </select>
                                    @error('metal_type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="purity" class="block text-sm font-medium text-gray-700">Purity *</label>
                                    <select name="purity" id="purity" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Purity</option>
                                        <!-- Options will be populated by JavaScript -->
                                    </select>
                                    @error('purity')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Rate Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Rate Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="rate_per_gram" class="block text-sm font-medium text-gray-700">Rate per Gram (₹) *</label>
                                    <input type="number" name="rate_per_gram" id="rate_per_gram" value="{{ old('rate_per_gram', $metalRate->rate_per_gram) }}" 
                                           step="0.01" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('rate_per_gram')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="rate_per_10_gram" class="block text-sm font-medium text-gray-700">Rate per 10 Grams (₹)</label>
                                    <input type="number" id="rate_per_10_gram" readonly
                                           class="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm">
                                    <p class="mt-1 text-xs text-gray-500">Automatically calculated</p>
                                </div>
                            </div>
                        </div>

                        <!-- Date and Status -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Date & Status</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="effective_date" class="block text-sm font-medium text-gray-700">Effective Date *</label>
                                    <input type="date" name="effective_date" id="effective_date" value="{{ old('effective_date', $metalRate->effective_date->format('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('effective_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="flex items-center mt-6">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="is_active" value="1" {{ old('is_active', $metalRate->is_active) ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm text-gray-700">Set as Active Rate</span>
                                    </label>
                                    <div class="ml-2">
                                        <div class="text-xs text-gray-500">
                                            This will deactivate other rates for the same metal/purity
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Current vs New Rate Comparison -->
                        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Rate Comparison</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Current Rate</h4>
                                    <div class="text-lg font-bold text-gray-900">₹{{ number_format($metalRate->rate_per_gram, 2) }}/g</div>
                                    <div class="text-sm text-gray-500">₹{{ number_format($metalRate->rate_per_10_gram, 2) }}/10g</div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">New Rate</h4>
                                    <div id="new-rate-display" class="text-lg font-bold text-blue-600">₹{{ number_format($metalRate->rate_per_gram, 2) }}/g</div>
                                    <div id="new-rate-10g-display" class="text-sm text-gray-500">₹{{ number_format($metalRate->rate_per_10_gram, 2) }}/10g</div>
                                    <div id="rate-change" class="text-sm mt-1"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('metal-rates.show', $metalRate) }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Metal Rate
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const metalTypeSelect = document.getElementById('metal_type');
            const puritySelect = document.getElementById('purity');
            const ratePerGramInput = document.getElementById('rate_per_gram');
            const ratePer10GramInput = document.getElementById('rate_per_10_gram');
            const newRateDisplay = document.getElementById('new-rate-display');
            const newRate10gDisplay = document.getElementById('new-rate-10g-display');
            const rateChangeDisplay = document.getElementById('rate-change');

            const currentRate = {{ $metalRate->rate_per_gram }};
            const currentPurity = '{{ $metalRate->purity }}';

            // Purity options based on metal type
            const purityOptions = {
                'Gold': ['22K', '18K', '14K'],
                'Silver': ['925', '999'],
                'Platinum': ['950', '900']
            };

            // Update purity options when metal type changes
            metalTypeSelect.addEventListener('change', function() {
                const selectedMetal = this.value;
                puritySelect.innerHTML = '<option value="">Select Purity</option>';
                
                if (selectedMetal && purityOptions[selectedMetal]) {
                    purityOptions[selectedMetal].forEach(purity => {
                        const option = document.createElement('option');
                        option.value = purity;
                        option.textContent = purity;
                        if (purity === currentPurity) {
                            option.selected = true;
                        }
                        puritySelect.appendChild(option);
                    });
                }
            });

            // Calculate rate per 10 grams and show comparison when rate per gram changes
            ratePerGramInput.addEventListener('input', function() {
                const newRate = parseFloat(this.value) || 0;
                const newRate10g = newRate * 10;
                
                // Update calculated field
                ratePer10GramInput.value = newRate10g.toFixed(2);
                
                // Update display
                newRateDisplay.textContent = '₹' + newRate.toFixed(2) + '/g';
                newRate10gDisplay.textContent = '₹' + newRate10g.toFixed(2) + '/10g';
                
                // Show rate change
                const change = newRate - currentRate;
                const changePercent = currentRate > 0 ? ((change / currentRate) * 100) : 0;
                
                if (change > 0) {
                    rateChangeDisplay.innerHTML = '<span class="text-green-600">↑ ₹' + change.toFixed(2) + ' (+' + changePercent.toFixed(2) + '%)</span>';
                } else if (change < 0) {
                    rateChangeDisplay.innerHTML = '<span class="text-red-600">↓ ₹' + Math.abs(change).toFixed(2) + ' (' + changePercent.toFixed(2) + '%)</span>';
                } else {
                    rateChangeDisplay.innerHTML = '<span class="text-gray-500">No change</span>';
                }
            });

            // Initialize purity options if metal type is already selected
            if (metalTypeSelect.value) {
                metalTypeSelect.dispatchEvent(new Event('change'));
            }

            // Initialize rate calculation if rate per gram is already filled
            if (ratePerGramInput.value) {
                ratePerGramInput.dispatchEvent(new Event('input'));
            }
        });
    </script>
</x-app-layout>
