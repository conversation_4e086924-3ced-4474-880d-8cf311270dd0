<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Metal Rates Management')); ?>

            </h2>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('calculator')); ?>" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Calculator
                </a>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create_metal_rate')): ?>
                    <a href="<?php echo e(route('metal-rates.create')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add New Rate
                    </a>
                <?php endif; ?>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <!-- Current Active Rates -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Active Rates</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <?php $__empty_1 = true; $__currentLoopData = $currentRates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $metalType => $purities): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 rounded-lg border border-yellow-200">
                                <h4 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e($metalType); ?></h4>
                                <?php $__currentLoopData = $purities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purity => $rate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-gray-700"><?php echo e($purity); ?></span>
                                        <div class="text-right">
                                            <div class="text-lg font-bold text-gray-900">₹<?php echo e(number_format($rate->rate_per_gram, 2)); ?>/g</div>
                                            <div class="text-xs text-gray-500">₹<?php echo e(number_format($rate->rate_per_10_gram, 2)); ?>/10g</div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <div class="text-xs text-gray-500 mt-2">
                                    Updated: <?php echo e($purities->first()->effective_date->format('d M, Y')); ?>

                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="col-span-3 text-center text-gray-500 py-8">
                                No active rates found. Please add some metal rates.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="<?php echo e(route('metal-rates.index')); ?>" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <select name="metal_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Metals</option>
                                <option value="Gold" <?php echo e(request('metal_type') === 'Gold' ? 'selected' : ''); ?>>Gold</option>
                                <option value="Silver" <?php echo e(request('metal_type') === 'Silver' ? 'selected' : ''); ?>>Silver</option>
                                <option value="Platinum" <?php echo e(request('metal_type') === 'Platinum' ? 'selected' : ''); ?>>Platinum</option>
                            </select>
                        </div>
                        <div>
                            <select name="purity" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Purities</option>
                                <option value="22K" <?php echo e(request('purity') === '22K' ? 'selected' : ''); ?>>22K</option>
                                <option value="18K" <?php echo e(request('purity') === '18K' ? 'selected' : ''); ?>>18K</option>
                                <option value="14K" <?php echo e(request('purity') === '14K' ? 'selected' : ''); ?>>14K</option>
                                <option value="925" <?php echo e(request('purity') === '925' ? 'selected' : ''); ?>>925</option>
                                <option value="999" <?php echo e(request('purity') === '999' ? 'selected' : ''); ?>>999</option>
                                <option value="950" <?php echo e(request('purity') === '950' ? 'selected' : ''); ?>>950</option>
                            </select>
                        </div>
                        <div>
                            <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" name="from_date" value="<?php echo e(request('from_date')); ?>" 
                                   placeholder="From Date"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Search
                            </button>
                            <a href="<?php echo e(route('metal-rates.index')); ?>" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Update Form -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create_metal_rate')): ?>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Rate Update</h3>
                        <form method="POST" action="<?php echo e(route('metal-rates.bulk-update')); ?>" id="bulkUpdateForm">
                            <?php echo csrf_field(); ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                                <div>
                                    <label for="effective_date" class="block text-sm font-medium text-gray-700">Effective Date</label>
                                    <input type="date" name="effective_date" id="effective_date" value="<?php echo e(today()->format('Y-m-d')); ?>" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>
                                <div class="flex items-end">
                                    <button type="button" id="addRateRow" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                        Add Rate
                                    </button>
                                </div>
                            </div>
                            
                            <div id="ratesContainer" class="space-y-3">
                                <!-- Rate rows will be added here -->
                            </div>
                            
                            <div class="mt-4">
                                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Update All Rates
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Rates History Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Rate History</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metal & Purity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per Gram</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per 10g</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Effective Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__empty_1 = true; $__currentLoopData = $rates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><?php echo e($rate->metal_type); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo e($rate->purity); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">₹<?php echo e(number_format($rate->rate_per_gram, 2)); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">₹<?php echo e(number_format($rate->rate_per_10_gram, 2)); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo e($rate->effective_date->format('d M, Y')); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($rate->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); ?>">
                                                <?php echo e($rate->is_active ? 'Active' : 'Inactive'); ?>

                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo e($rate->createdBy->name); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo e($rate->created_at->format('d M, Y')); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_metal_rates')): ?>
                                                    <a href="<?php echo e(route('metal-rates.show', $rate)); ?>" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_metal_rate')): ?>
                                                    <a href="<?php echo e(route('metal-rates.edit', $rate)); ?>" class="text-blue-600 hover:text-blue-900">Edit</a>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete_metal_rate')): ?>
                                                    <button type="button" onclick="openDeleteModal(<?php echo e($rate->id); ?>, '<?php echo e($rate->metal_type); ?>', '<?php echo e($rate->purity); ?>', '<?php echo e($rate->effective_date->format('d M Y')); ?>')"
                                                            class="text-red-600 hover:text-red-900">Delete</button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            No metal rates found.
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        <?php echo e($rates->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const addRateRowButton = document.getElementById('addRateRow');
            const ratesContainer = document.getElementById('ratesContainer');
            let rateIndex = 0;

            addRateRowButton.addEventListener('click', function() {
                const rateRow = document.createElement('div');
                rateRow.className = 'grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg';
                rateRow.innerHTML = `
                    <div>
                        <select name="rates[${rateIndex}][metal_type]" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="">Select Metal</option>
                            <option value="Gold">Gold</option>
                            <option value="Silver">Silver</option>
                            <option value="Platinum">Platinum</option>
                        </select>
                    </div>
                    <div>
                        <select name="rates[${rateIndex}][purity]" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="">Select Purity</option>
                            <option value="22K">22K</option>
                            <option value="18K">18K</option>
                            <option value="14K">14K</option>
                            <option value="925">925</option>
                            <option value="999">999</option>
                            <option value="950">950</option>
                            <option value="900">900</option>
                        </select>
                    </div>
                    <div>
                        <input type="number" name="rates[${rateIndex}][rate_per_gram]" placeholder="Rate per gram" step="0.01" min="0" required
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    </div>
                    <div class="flex items-center">
                        <button type="button" class="remove-rate bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            Remove
                        </button>
                    </div>
                `;

                rateRow.querySelector('.remove-rate').addEventListener('click', function() {
                    rateRow.remove();
                });

                ratesContainer.appendChild(rateRow);
                rateIndex++;
            });

            // Add initial rate row
            addRateRowButton.click();
        });

        // Delete Modal Functions
        let deleteRateId = null;

        function openDeleteModal(rateId, metalType, purity, effectiveDate) {
            deleteRateId = rateId;
            document.getElementById('deleteMetalType').textContent = metalType;
            document.getElementById('deletePurity').textContent = purity;
            document.getElementById('deleteEffectiveDate').textContent = effectiveDate;
            document.getElementById('deleteModal').classList.remove('hidden');
        }

        function closeDeleteModal() {
            deleteRateId = null;
            document.getElementById('deleteModal').classList.add('hidden');
        }

        function confirmDelete() {
            if (deleteRateId) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/metal-rates/${deleteRateId}`;

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '<?php echo e(csrf_token()); ?>';

                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Close modal when clicking outside
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });
    </script>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Metal Rate</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        Are you sure you want to delete this metal rate?
                    </p>
                    <div class="mt-3 p-3 bg-gray-50 rounded-lg">
                        <div class="text-sm">
                            <div class="flex justify-between mb-1">
                                <span class="font-medium">Metal Type:</span>
                                <span id="deleteMetalType"></span>
                            </div>
                            <div class="flex justify-between mb-1">
                                <span class="font-medium">Purity:</span>
                                <span id="deletePurity"></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">Effective Date:</span>
                                <span id="deleteEffectiveDate"></span>
                            </div>
                        </div>
                    </div>
                    <p class="text-xs text-red-600 mt-2">
                        This action cannot be undone.
                    </p>
                </div>
                <div class="flex justify-center space-x-4 px-4 py-3">
                    <button onclick="closeDeleteModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
                        Cancel
                    </button>
                    <button onclick="confirmDelete()"
                            class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\metal-rates\index.blade.php ENDPATH**/ ?>