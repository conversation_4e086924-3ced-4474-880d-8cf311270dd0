<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scheme Certificate - <?php echo e($savingScheme->scheme_number); ?></title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 30px;
            font-size: 14px;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        .certificate {
            background: white;
            border: 8px solid #d4af37;
            border-radius: 15px;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }
        .certificate::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 2px solid #d4af37;
            border-radius: 10px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            z-index: 1;
        }
        .company-name {
            font-size: 32px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .company-tagline {
            font-size: 14px;
            color: #666;
            font-style: italic;
            margin-bottom: 20px;
        }
        .certificate-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin: 30px 0;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            border-bottom: 3px solid #d4af37;
            padding-bottom: 10px;
        }
        .certificate-content {
            text-align: center;
            margin: 40px 0;
            font-size: 16px;
            line-height: 2;
        }
        .customer-name {
            font-size: 24px;
            font-weight: bold;
            color: #d4af37;
            text-decoration: underline;
            margin: 20px 0;
        }
        .scheme-details {
            background: #f9f9f9;
            border: 2px solid #d4af37;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        .scheme-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .scheme-details td {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .scheme-details .label {
            font-weight: bold;
            width: 40%;
            color: #333;
        }
        .scheme-details .value {
            color: #666;
        }
        .amount-highlight {
            font-size: 20px;
            font-weight: bold;
            color: #d4af37;
            text-align: center;
            background: #fff;
            border: 2px solid #d4af37;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        .signatures {
            display: table;
            width: 100%;
            margin-top: 60px;
        }
        .signature-left, .signature-right {
            display: table-cell;
            width: 50%;
            text-align: center;
            vertical-align: bottom;
        }
        .signature-line {
            border-top: 2px solid #333;
            width: 200px;
            margin: 0 auto 10px;
        }
        .signature-title {
            font-weight: bold;
            color: #333;
        }
        .certificate-footer {
            text-align: center;
            margin-top: 40px;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .seal {
            position: absolute;
            top: 50px;
            right: 50px;
            width: 100px;
            height: 100px;
            border: 3px solid #d4af37;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            font-size: 12px;
            font-weight: bold;
            color: #d4af37;
            text-align: center;
            line-height: 1.2;
        }
        .decorative-border {
            border: 2px solid #d4af37;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            background: linear-gradient(45deg, #fff 0%, #f9f9f9 100%);
        }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="seal">
            OFFICIAL<br>SEAL
        </div>
        
        <div class="header">
            <div class="company-name"><?php echo e(config('app.name', 'Jewel Pro')); ?></div>
            <div class="company-tagline">Premium Jewelry Store</div>
            <div style="font-size: 12px; color: #666;">
                123 Main Street, City, State - 123456<br>
                Phone: +91 98765 43210 | Email: <EMAIL><br>
                GSTIN: 29ABCDE1234F1Z5
            </div>
        </div>

        <div class="certificate-title">Certificate of Completion</div>

        <div class="certificate-content">
            This is to certify that<br>
            <div class="customer-name"><?php echo e(strtoupper($savingScheme->customer->name)); ?></div>
            has successfully completed the<br>
            <strong><?php echo e($savingScheme->scheme_name); ?> Saving Scheme</strong><br>
            and is entitled to the benefits as per the scheme terms.
        </div>

        <div class="decorative-border">
            <div class="scheme-details">
                <table>
                    <tr>
                        <td class="label">Scheme Number:</td>
                        <td class="value"><?php echo e($savingScheme->scheme_number); ?></td>
                    </tr>
                    <tr>
                        <td class="label">Scheme Type:</td>
                        <td class="value"><?php echo e(ucfirst(str_replace('_', ' ', $savingScheme->scheme_type))); ?></td>
                    </tr>
                    <tr>
                        <td class="label">Start Date:</td>
                        <td class="value"><?php echo e($savingScheme->start_date->format('d/m/Y')); ?></td>
                    </tr>
                    <tr>
                        <td class="label">Maturity Date:</td>
                        <td class="value"><?php echo e($savingScheme->maturity_date->format('d/m/Y')); ?></td>
                    </tr>
                    <tr>
                        <td class="label">Duration:</td>
                        <td class="value"><?php echo e($savingScheme->duration_months); ?> months</td>
                    </tr>
                    <tr>
                        <td class="label">Monthly Installment:</td>
                        <td class="value">₹<?php echo e(number_format($savingScheme->monthly_amount, 2)); ?></td>
                    </tr>
                    <tr>
                        <td class="label">Total Paid Amount:</td>
                        <td class="value">₹<?php echo e(number_format($savingScheme->payments->sum('amount'), 2)); ?></td>
                    </tr>
                    <tr>
                        <td class="label">Bonus Amount:</td>
                        <td class="value">₹<?php echo e(number_format($savingScheme->bonus_amount, 2)); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="amount-highlight">
            Total Maturity Value: ₹<?php echo e(number_format($savingScheme->maturity_amount, 2)); ?>

        </div>

        <div style="text-align: center; margin: 30px 0; font-size: 14px; color: #666;">
            This certificate is issued on <?php echo e(date('d/m/Y')); ?> and serves as proof of scheme completion.<br>
            The holder is entitled to purchase jewelry worth the maturity value from our store.
        </div>

        <div class="signatures">
            <div class="signature-left">
                <div class="signature-line"></div>
                <div class="signature-title">Customer Signature</div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    <?php echo e($savingScheme->customer->name); ?>

                </div>
            </div>
            <div class="signature-right">
                <div class="signature-line"></div>
                <div class="signature-title">Authorized Signatory</div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    <?php echo e(config('app.name', 'Jewel Pro')); ?>

                </div>
            </div>
        </div>

        <div class="certificate-footer">
            <strong>Terms & Conditions:</strong><br>
            1. This certificate is valid for jewelry purchase only and cannot be encashed.<br>
            2. The certificate must be presented at the time of purchase.<br>
            3. Any balance amount can be adjusted in future purchases within 1 year.<br>
            4. This certificate is non-transferable and valid only for the named customer.<br>
            5. In case of loss, a duplicate certificate can be issued with proper verification.<br><br>
            
            <strong>Certificate ID:</strong> <?php echo e($savingScheme->scheme_number); ?>-<?php echo e(date('Y')); ?><br>
            <strong>Issue Date:</strong> <?php echo e(date('d/m/Y')); ?><br>
            <strong>Valid Until:</strong> <?php echo e(date('d/m/Y', strtotime('+1 year'))); ?>

        </div>
    </div>
</body>
</html>
<?php /**PATH C:\proj\jewel-pro\resources\views\saving-schemes\certificate.blade.php ENDPATH**/ ?>