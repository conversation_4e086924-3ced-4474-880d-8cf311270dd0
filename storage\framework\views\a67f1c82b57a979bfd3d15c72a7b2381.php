<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="h-full bg-gray-50">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e(config('app.name', 'Jewel Pro')); ?> - Professional Jewelry Management</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    <style>
        /* Professional Dashboard Styles */
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .sidebar-scroll {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
        }
        
        .sidebar-scroll::-webkit-scrollbar {
            width: 4px;
        }
        
        .sidebar-scroll::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .sidebar-scroll::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }
        
        .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .nav-item {
            transition: all 0.2s ease-in-out;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }
        
        .nav-item.active {
            background: rgba(255, 255, 255, 0.15);
            border-right: 3px solid #fbbf24;
        }
        
        .ticker-scroll {
            animation: scroll 30s linear infinite;
        }
        
        @keyframes scroll {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .gold-gradient {
            background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
        }
        
        .silver-gradient {
            background: linear-gradient(135deg, #e2e8f0 0%, #94a3b8 100%);
        }
        
        .success-gradient {
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
        }
        
        .danger-gradient {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .info-gradient {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .sidebar-brand {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
        }
        
        .main-sidebar {
            background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 50%, #581c87 100%);
            box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);
        }
        
        .content-area {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        
        .header-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(226, 232, 240, 0.8);
        }
        
        .notification-badge {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .quick-action-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .quick-action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Mobile Responsive Improvements */
        @media (max-width: 768px) {
            .sidebar-scroll {
                padding-bottom: 2rem;
            }

            .ticker-scroll {
                animation: none;
                overflow-x: auto;
                white-space: nowrap;
            }

            .stat-card {
                padding: 1rem;
            }

            .quick-action-btn {
                padding: 0.75rem;
            }

            .card-hover:hover {
                transform: none;
            }
        }

        @media (max-width: 640px) {
            .main-sidebar {
                width: 16rem;
            }

            .header-glass {
                padding: 0.5rem 1rem;
            }

            .content-area {
                padding: 1rem;
            }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .nav-item:hover {
                background: transparent;
                transform: none;
            }

            .card-hover:hover {
                transform: none;
                box-shadow: none;
            }

            .quick-action-btn:hover {
                background: rgba(255, 255, 255, 0.1);
                transform: none;
            }
        }

        /* Professional Animations */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-left {
            animation: slideInLeft 0.5s ease-out;
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .scale-in {
            animation: scaleIn 0.4s ease-out;
        }

        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        /* Professional Focus States */
        .focus-ring:focus {
            outline: none;
            ring: 2px;
            ring-color: #3b82f6;
            ring-offset: 2px;
        }

        /* Custom Scrollbar for Main Content */
        .content-area::-webkit-scrollbar {
            width: 8px;
        }

        .content-area::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        .content-area::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .content-area::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Professional Loading States */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Enhanced Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #374151;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background: white;
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="h-full overflow-hidden">
    <div id="app" x-data="dashboardApp()" class="h-full flex">
        <!-- Sidebar -->
        <?php echo $__env->make('layouts.components.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col min-w-0">
            <!-- Header -->
            <?php echo $__env->make('layouts.components.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            
            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto content-area">
                <!-- Page Heading (for compatibility with old pages) -->
                <?php if(isset($header)): ?>
                    <div class="bg-white shadow-sm border-b border-gray-200">
                        <div class="px-6 py-4">
                            <?php echo e($header); ?>

                        </div>
                    </div>
                    <!-- Legacy page content with modern wrapper -->
                    <div class="modern-content">
                        <div class="legacy-content-wrapper">
                            <?php echo e($slot); ?>

                        </div>
                    </div>
                <?php else: ?>
                    <!-- Modern dashboard structure -->
                    <?php echo e($slot); ?>

                <?php endif; ?>
            </main>
        </div>
    </div>

    <script>
        function dashboardApp() {
            return {
                sidebarOpen: false,
                currentTime: new Date().toLocaleTimeString(),
                
                init() {
                    // Update time every second
                    setInterval(() => {
                        this.currentTime = new Date().toLocaleTimeString();
                    }, 1000);
                },
                
                toggleSidebar() {
                    this.sidebarOpen = !this.sidebarOpen;
                },
                
                closeSidebar() {
                    this.sidebarOpen = false;
                }
            }
        }
    </script>
</body>
</html>
<?php /**PATH C:\proj\jewel-pro\resources\views\layouts\dashboard.blade.php ENDPATH**/ ?>