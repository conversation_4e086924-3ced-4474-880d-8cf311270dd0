<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Customer Details')); ?> - <?php echo e($customer->name); ?>

            </h2>
            <div class="flex space-x-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_customer')): ?>
                    <a href="<?php echo e(route('customers.edit', $customer)); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Customer
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('customers.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Customers
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Customer Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Basic Info -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->name); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Mobile</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->mobile); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->email ?: '-'); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->date_of_birth?->format('d M, Y') ?: '-'); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Anniversary</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->anniversary_date?->format('d M, Y') ?: '-'); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($customer->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                            <?php echo e($customer->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Address Info -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Address Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Address</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->address ?: '-'); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">City</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->city ?: '-'); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">State</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->state ?: '-'); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Pincode</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->pincode ?: '-'); ?></dd>
                                </div>
                            </dl>
                        </div>

                        <!-- KYC & Stats -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">KYC & Statistics</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">KYC Type</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->kyc_type ?: '-'); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">KYC Number</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->kyc_number ?: '-'); ?></dd>
                                </div>
                                <?php if($customer->kyc_document_path): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">KYC Document</dt>
                                        <dd class="text-sm">
                                            <a href="<?php echo e(asset('storage/' . $customer->kyc_document_path)); ?>" target="_blank" class="text-blue-600 hover:text-blue-800">View Document</a>
                                        </dd>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Total Purchases</dt>
                                    <dd class="text-sm text-gray-900">₹<?php echo e(number_format($customer->total_purchases, 2)); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Total Orders</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->total_orders); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Member Since</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($customer->created_at->format('d M, Y')); ?></dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <?php if($customer->notes): ?>
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                            <p class="text-sm text-gray-700"><?php echo e($customer->notes); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Transaction History Tabs -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active" data-tab="sales">
                            Sales (<?php echo e($customer->sales->count()); ?>)
                        </button>
                        <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="estimates">
                            Estimates (<?php echo e($customer->estimates->count()); ?>)
                        </button>
                        <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="repairs">
                            Repairs (<?php echo e($customer->repairs->count()); ?>)
                        </button>
                        <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="schemes">
                            Schemes (<?php echo e($customer->savingSchemes->count()); ?>)
                        </button>
                    </nav>
                </div>

                <!-- Sales Tab -->
                <div id="sales-tab" class="tab-content p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Sales History</h3>
                    <?php if($customer->sales->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $customer->sales->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo e($sale->invoice_number); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo e($sale->sale_date->format('d M, Y')); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹<?php echo e(number_format($sale->total_amount, 2)); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    <?php echo e(ucfirst($sale->payment_status)); ?>

                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500">No sales records found.</p>
                    <?php endif; ?>
                </div>

                <!-- Other tabs content would go here -->
                <div id="estimates-tab" class="tab-content p-6 hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Estimates History</h3>
                    <p class="text-gray-500">Estimates will be displayed here once the estimates module is implemented.</p>
                </div>

                <div id="repairs-tab" class="tab-content p-6 hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Repairs History</h3>
                    <p class="text-gray-500">Repairs will be displayed here once the repairs module is implemented.</p>
                </div>

                <div id="schemes-tab" class="tab-content p-6 hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Saving Schemes</h3>
                    <p class="text-gray-500">Saving schemes will be displayed here once the schemes module is implemented.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabName = this.getAttribute('data-tab');
                    
                    // Remove active class from all buttons
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-indigo-500', 'text-indigo-600', 'active');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });
                    
                    // Add active class to clicked button
                    this.classList.remove('border-transparent', 'text-gray-500');
                    this.classList.add('border-indigo-500', 'text-indigo-600', 'active');
                    
                    // Hide all tab contents
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // Show selected tab content
                    document.getElementById(tabName + '-tab').classList.remove('hidden');
                });
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\customers\show.blade.php ENDPATH**/ ?>