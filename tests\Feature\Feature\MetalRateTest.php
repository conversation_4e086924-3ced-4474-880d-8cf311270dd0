<?php

namespace Tests\Feature\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\MetalRate;

class MetalRateTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user for authentication
        $this->user = User::factory()->create();
    }

    /**
     * Test metal rates index page loads successfully.
     */
    public function test_metal_rates_index_loads_successfully(): void
    {
        $response = $this->actingAs($this->user)->get(route('metal-rates.index'));
        $response->assertStatus(200);
        $response->assertViewIs('metal-rates.index');
    }

    /**
     * Test metal rates create page loads successfully.
     */
    public function test_metal_rates_create_loads_successfully(): void
    {
        $response = $this->actingAs($this->user)->get(route('metal-rates.create'));
        $response->assertStatus(200);
        $response->assertViewIs('metal-rates.create');
    }

    /**
     * Test creating a new metal rate.
     */
    public function test_can_create_metal_rate(): void
    {
        $metalRateData = [
            'metal_type' => 'Gold',
            'purity' => '22K',
            'rate_per_gram' => 5000.00,
            'effective_date' => now()->format('Y-m-d'),
            'is_active' => true,
        ];

        $response = $this->withoutMiddleware()
            ->actingAs($this->user)
            ->post(route('metal-rates.store'), $metalRateData);

        $response->assertRedirect(route('metal-rates.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('metal_rates', [
            'metal_type' => 'Gold',
            'purity' => '22K',
            'rate_per_gram' => 5000.00,
            'rate_per_10_gram' => 50000.00,
            'is_active' => true,
        ]);
    }

    /**
     * Test validation errors when creating metal rate with invalid data.
     */
    public function test_metal_rate_creation_validation(): void
    {
        $invalidData = [
            'metal_type' => '',
            'purity' => '',
            'rate_per_gram' => -100,
            'effective_date' => '',
        ];

        $response = $this->withoutMiddleware()
            ->actingAs($this->user)
            ->post(route('metal-rates.store'), $invalidData);

        $response->assertSessionHasErrors(['metal_type', 'purity', 'rate_per_gram', 'effective_date']);
    }

    /**
     * Test viewing a metal rate.
     */
    public function test_can_view_metal_rate(): void
    {
        $metalRate = MetalRate::factory()->create([
            'created_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('metal-rates.show', $metalRate));

        $response->assertStatus(200);
        $response->assertViewIs('metal-rates.show');
        $response->assertViewHas('metalRate', $metalRate);
    }

    /**
     * Test editing a metal rate.
     */
    public function test_can_edit_metal_rate(): void
    {
        $metalRate = MetalRate::factory()->create([
            'created_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('metal-rates.edit', $metalRate));

        $response->assertStatus(200);
        $response->assertViewIs('metal-rates.edit');
        $response->assertViewHas('metalRate', $metalRate);
    }

    /**
     * Test updating a metal rate.
     */
    public function test_can_update_metal_rate(): void
    {
        $metalRate = MetalRate::factory()->create([
            'created_by' => $this->user->id,
            'rate_per_gram' => 5000.00,
        ]);

        $updateData = [
            'metal_type' => $metalRate->metal_type,
            'purity' => $metalRate->purity,
            'rate_per_gram' => 5500.00,
            'effective_date' => $metalRate->effective_date->format('Y-m-d'),
            'is_active' => true,
        ];

        $response = $this->withoutMiddleware()
            ->actingAs($this->user)
            ->put(route('metal-rates.update', $metalRate), $updateData);

        $response->assertRedirect(route('metal-rates.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('metal_rates', [
            'id' => $metalRate->id,
            'rate_per_gram' => 5500.00,
            'rate_per_10_gram' => 55000.00,
        ]);
    }

    /**
     * Test deleting a metal rate.
     */
    public function test_can_delete_metal_rate(): void
    {
        $metalRate = MetalRate::factory()->create([
            'created_by' => $this->user->id,
        ]);

        $response = $this->withoutMiddleware()
            ->actingAs($this->user)
            ->delete(route('metal-rates.destroy', $metalRate));

        $response->assertRedirect(route('metal-rates.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseMissing('metal_rates', [
            'id' => $metalRate->id,
        ]);
    }
}
