<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\MetalRate;

class MetalRateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $metalRate = $this->route('metal_rate');

        return [
            'metal_type' => [
                'required',
                'string',
                Rule::in(['Gold', 'Silver', 'Platinum'])
            ],
            'purity' => [
                'required',
                'string',
                'max:10',
                function ($attribute, $value, $fail) {
                    $metalType = $this->input('metal_type');
                    $validPurities = [
                        'Gold' => ['22K', '18K', '14K'],
                        'Silver' => ['925', '999'],
                        'Platinum' => ['950', '900']
                    ];

                    if ($metalType && isset($validPurities[$metalType]) && !in_array($value, $validPurities[$metalType])) {
                        $fail("The selected purity is not valid for {$metalType}.");
                    }
                }
            ],
            'rate_per_gram' => [
                'required',
                'numeric',
                'min:0.01',
                'max:999999.99'
            ],
            'effective_date' => [
                'required',
                'date',
                'before_or_equal:' . now()->addDays(30)->format('Y-m-d'), // Allow future dates up to 30 days
                function ($attribute, $value, $fail) use ($metalRate) {
                    // Check for duplicate rates on the same date for same metal/purity
                    $query = MetalRate::where('metal_type', $this->input('metal_type'))
                        ->where('purity', $this->input('purity'))
                        ->whereDate('effective_date', $value);

                    if ($metalRate) {
                        $query->where('id', '!=', $metalRate->id);
                    }

                    if ($query->exists()) {
                        $fail('A rate for this metal type and purity already exists for the selected date.');
                    }
                }
            ],
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'metal_type.required' => 'Please select a metal type.',
            'metal_type.in' => 'Please select a valid metal type.',
            'purity.required' => 'Please select a purity level.',
            'rate_per_gram.required' => 'Please enter the rate per gram.',
            'rate_per_gram.numeric' => 'Rate per gram must be a valid number.',
            'rate_per_gram.min' => 'Rate per gram must be greater than 0.',
            'rate_per_gram.max' => 'Rate per gram cannot exceed ₹999,999.99.',
            'effective_date.required' => 'Please select an effective date.',
            'effective_date.date' => 'Please enter a valid date.',
            'effective_date.before_or_equal' => 'Effective date cannot be more than 30 days in the future.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'metal_type' => 'metal type',
            'purity' => 'purity level',
            'rate_per_gram' => 'rate per gram',
            'effective_date' => 'effective date',
            'is_active' => 'active status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure is_active is properly handled
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => false]);
        }
    }
}
