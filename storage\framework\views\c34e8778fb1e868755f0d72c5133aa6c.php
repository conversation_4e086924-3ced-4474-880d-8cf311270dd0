<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Product Barcode')); ?> - <?php echo e($product->name); ?>

            </h2>
            <div class="flex space-x-2">
                <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Print Barcode
                </button>
                <a href="<?php echo e(route('products.show', $product)); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Product
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Barcode Display -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="text-center">
                        <h3 class="text-lg font-medium text-gray-900 mb-6">Product Barcode</h3>
                        
                        <!-- Large Barcode for Display -->
                        <div class="mb-6 p-6 border-2 border-dashed border-gray-300 rounded-lg">
                            <div class="mb-4">
                                <img src="data:image/png;base64,<?php echo e($barcodeImage); ?>" alt="Barcode" class="mx-auto">
                            </div>
                            <div class="text-lg font-mono font-bold text-gray-900 mb-2"><?php echo e($product->barcode); ?></div>
                            <div class="text-sm text-gray-600"><?php echo e($product->name); ?></div>
                            <div class="text-sm text-gray-600"><?php echo e($product->metal_type); ?> <?php echo e($product->purity); ?> - <?php echo e($product->gross_weight); ?>g</div>
                            <div class="text-lg font-semibold text-gray-900 mt-2">₹<?php echo e(number_format($product->selling_price, 2)); ?></div>
                            <?php if($product->huid_number): ?>
                                <div class="text-xs text-blue-600 mt-1">HUID: <?php echo e($product->huid_number); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Printable Barcode Labels -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Printable Barcode Labels</h3>
                    
                    <!-- Multiple barcode labels for printing -->
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4 print-labels">
                        <?php for($i = 1; $i <= 6; $i++): ?>
                            <div class="border border-gray-300 p-3 text-center barcode-label">
                                <div class="mb-2">
                                    <img src="data:image/png;base64,<?php echo e($barcodeImage); ?>" alt="Barcode" class="mx-auto" style="height: 30px;">
                                </div>
                                <div class="text-xs font-mono font-bold"><?php echo e($product->barcode); ?></div>
                                <div class="text-xs truncate"><?php echo e($product->name); ?></div>
                                <div class="text-xs"><?php echo e($product->metal_type); ?> <?php echo e($product->purity); ?></div>
                                <div class="text-xs"><?php echo e($product->gross_weight); ?>g</div>
                                <div class="text-xs font-semibold">₹<?php echo e(number_format($product->selling_price, 2)); ?></div>
                                <?php if($product->huid_number): ?>
                                    <div class="text-xs text-blue-600">HUID: <?php echo e($product->huid_number); ?></div>
                                <?php endif; ?>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        @media print {
            /* Hide everything except barcode labels when printing */
            body * {
                visibility: hidden;
            }
            
            .print-labels, .print-labels * {
                visibility: visible;
            }
            
            .print-labels {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            
            .barcode-label {
                page-break-inside: avoid;
                margin: 5px;
                padding: 10px;
                border: 1px solid #000;
            }
            
            /* Hide header and other elements */
            header, nav, .py-12 > div > div:first-child {
                display: none !important;
            }
        }
        
        .barcode-label {
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\products\barcode.blade.php ENDPATH**/ ?>