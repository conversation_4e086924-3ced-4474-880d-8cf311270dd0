<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MetalRate;
use App\Http\Requests\MetalRateRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class MetalRateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission checks for testing
        // $this->middleware('permission:view_metal_rates')->only(['index', 'show', 'calculator']);
        // $this->middleware('permission:create_metal_rate')->only(['create', 'store']);
        // $this->middleware('permission:edit_metal_rate')->only(['edit', 'update']);
        // $this->middleware('permission:delete_metal_rate')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = MetalRate::with(['createdBy']);

        // Filter by metal type
        if ($request->filled('metal_type')) {
            $query->where('metal_type', $request->metal_type);
        }

        // Filter by purity
        if ($request->filled('purity')) {
            $query->where('purity', $request->purity);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('effective_date', '>=', $request->from_date);
        }
        if ($request->filled('to_date')) {
            $query->whereDate('effective_date', '<=', $request->to_date);
        }

        $rates = $query->latest('effective_date')->paginate(15);

        // Get current active rates for display
        $currentRates = MetalRate::active()
            ->current()
            ->get()
            ->groupBy('metal_type')
            ->map(function ($metalGroup) {
                return $metalGroup->groupBy('purity')->map(function ($purityGroup) {
                    return $purityGroup->first();
                });
            });

        return view('metal-rates.index', compact('rates', 'currentRates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('metal-rates.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(MetalRateRequest $request)
    {
        try {
            $validated = $request->validated();

            // Calculate rate per 10 grams
            $validated['rate_per_10_gram'] = $validated['rate_per_gram'] * 10;
            $validated['created_by'] = auth()->id();

            DB::transaction(function () use ($validated) {
                // If this is set as active, deactivate other rates for same metal/purity
                if ($validated['is_active'] ?? false) {
                    MetalRate::where('metal_type', $validated['metal_type'])
                             ->where('purity', $validated['purity'])
                             ->update(['is_active' => false]);
                }

                MetalRate::create($validated);
            });

            // Clear cache when rates are updated
            Cache::forget('current_metal_rates');

            return redirect()->route('metal-rates.index')
                ->with('success', 'Metal rate created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create metal rate. Please try again.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(MetalRate $metalRate)
    {
        $metalRate->load(['createdBy']);

        // Get rate history for same metal/purity
        $rateHistory = MetalRate::where('metal_type', $metalRate->metal_type)
            ->where('purity', $metalRate->purity)
            ->where('id', '!=', $metalRate->id)
            ->orderBy('effective_date', 'desc')
            ->limit(10)
            ->get();

        return view('metal-rates.show', compact('metalRate', 'rateHistory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MetalRate $metalRate)
    {
        return view('metal-rates.edit', compact('metalRate'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(MetalRateRequest $request, MetalRate $metalRate)
    {
        try {
            $validated = $request->validated();

            // Calculate rate per 10 grams
            $validated['rate_per_10_gram'] = $validated['rate_per_gram'] * 10;

            DB::transaction(function () use ($validated, $metalRate) {
                // If this is set as active, deactivate other rates for same metal/purity
                if ($validated['is_active'] ?? false) {
                    MetalRate::where('metal_type', $validated['metal_type'])
                             ->where('purity', $validated['purity'])
                             ->where('id', '!=', $metalRate->id)
                             ->update(['is_active' => false]);
                }

                $metalRate->update($validated);
            });

            // Clear cache when rates are updated
            Cache::forget('current_metal_rates');

            return redirect()->route('metal-rates.index')
                ->with('success', 'Metal rate updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update metal rate. Please try again.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MetalRate $metalRate)
    {
        try {
            // Check if this rate is currently being used in any calculations or transactions
            // You might want to add additional checks here based on your business logic

            $metalType = $metalRate->metal_type;
            $purity = $metalRate->purity;

            DB::transaction(function () use ($metalRate) {
                $metalRate->delete();
            });

            // Clear cache when rates are updated
            Cache::forget('current_metal_rates');

            return redirect()->route('metal-rates.index')
                ->with('success', "Metal rate for {$metalType} {$purity} deleted successfully.");
        } catch (\Exception $e) {
            return redirect()->route('metal-rates.index')
                ->with('error', 'Failed to delete metal rate. It may be in use by other records.');
        }
    }

    /**
     * Calculator tools interface
     */
    public function calculator()
    {
        // Get current active rates
        $currentRates = MetalRate::active()
            ->current()
            ->get()
            ->groupBy('metal_type')
            ->map(function ($metalGroup) {
                return $metalGroup->groupBy('purity')->map(function ($purityGroup) {
                    return $purityGroup->first();
                });
            });

        return view('metal-rates.calculator', compact('currentRates'));
    }

    /**
     * Calculate jewelry pricing
     */
    public function calculatePrice(Request $request)
    {
        $validated = $request->validate([
            'metal_type' => 'required|string',
            'purity' => 'required|string',
            'gross_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:100',
            'making_charges' => 'nullable|numeric|min:0',
            'stone_charges' => 'nullable|numeric|min:0',
            'custom_rate' => 'nullable|numeric|min:0',
        ]);

        // Get current rate or use custom rate
        $rate = $validated['custom_rate'] ?? null;
        if (!$rate) {
            $metalRate = MetalRate::where('metal_type', $validated['metal_type'])
                ->where('purity', $validated['purity'])
                ->where('is_active', true)
                ->first();

            if (!$metalRate) {
                return response()->json(['error' => 'No active rate found for selected metal/purity'], 400);
            }

            $rate = $metalRate->rate_per_gram;
        }

        // Calculate weights
        $grossWeight = $validated['gross_weight'];
        $stoneWeight = $validated['stone_weight'] ?? 0;
        $netWeight = $grossWeight - $stoneWeight;

        $wastagePercentage = $validated['wastage_percentage'] ?? 0;
        $wastageWeight = ($netWeight * $wastagePercentage) / 100;
        $totalWeight = $netWeight + $wastageWeight;

        // Calculate amounts
        $metalValue = $totalWeight * $rate;
        $makingCharges = $validated['making_charges'] ?? 0;
        $stoneCharges = $validated['stone_charges'] ?? 0;
        $subtotal = $metalValue + $makingCharges + $stoneCharges;

        // Calculate GST (3% for jewelry)
        $gstAmount = $subtotal * 0.03;
        $totalAmount = $subtotal + $gstAmount;

        return response()->json([
            'success' => true,
            'calculations' => [
                'gross_weight' => $grossWeight,
                'stone_weight' => $stoneWeight,
                'net_weight' => $netWeight,
                'wastage_percentage' => $wastagePercentage,
                'wastage_weight' => round($wastageWeight, 3),
                'total_weight' => round($totalWeight, 3),
                'rate_per_gram' => $rate,
                'metal_value' => round($metalValue, 2),
                'making_charges' => $makingCharges,
                'stone_charges' => $stoneCharges,
                'subtotal' => round($subtotal, 2),
                'gst_amount' => round($gstAmount, 2),
                'total_amount' => round($totalAmount, 2),
            ]
        ]);
    }

    /**
     * Get current rates API
     */
    public function getCurrentRates()
    {
        $rates = Cache::remember('current_metal_rates', 300, function () { // Cache for 5 minutes
            return MetalRate::active()
                ->current()
                ->get()
                ->groupBy('metal_type')
                ->map(function ($metalGroup) {
                    return $metalGroup->groupBy('purity')->map(function ($purityGroup) {
                        $rate = $purityGroup->first();
                        return [
                            'id' => $rate->id,
                            'metal_type' => $rate->metal_type,
                            'purity' => $rate->purity,
                            'rate_per_gram' => $rate->rate_per_gram,
                            'rate_per_10_gram' => $rate->rate_per_10_gram,
                            'effective_date' => $rate->effective_date->format('Y-m-d'),
                        ];
                    });
                });
        });

        return response()->json($rates);
    }

    /**
     * Bulk update rates
     */
    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'rates' => 'required|array',
            'rates.*.metal_type' => 'required|string',
            'rates.*.purity' => 'required|string',
            'rates.*.rate_per_gram' => 'required|numeric|min:0',
            'effective_date' => 'required|date',
        ]);

        DB::transaction(function () use ($validated) {
            foreach ($validated['rates'] as $rateData) {
                // Deactivate existing rates
                MetalRate::where('metal_type', $rateData['metal_type'])
                         ->where('purity', $rateData['purity'])
                         ->update(['is_active' => false]);

                // Create new rate
                MetalRate::create([
                    'metal_type' => $rateData['metal_type'],
                    'purity' => $rateData['purity'],
                    'rate_per_gram' => $rateData['rate_per_gram'],
                    'rate_per_10_gram' => $rateData['rate_per_gram'] * 10,
                    'effective_date' => $validated['effective_date'],
                    'is_active' => true,
                    'created_by' => auth()->id(),
                ]);
            }
        });

        return redirect()->route('metal-rates.index')
            ->with('success', 'Metal rates updated successfully.');
    }
}
