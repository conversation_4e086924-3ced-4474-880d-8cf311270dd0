<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Settings & Configuration')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <!-- Settings Overview -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">System Configuration</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600"><?php echo e($settings['business_name']); ?></div>
                            <div class="text-sm text-gray-500">Business Name</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600"><?php echo e($settings['currency_symbol']); ?></div>
                            <div class="text-sm text-gray-500">Currency</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600"><?php echo e($settings['cgst_rate'] + $settings['sgst_rate']); ?>%</div>
                            <div class="text-sm text-gray-500">GST Rate</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600"><?php echo e($settings['timezone']); ?></div>
                            <div class="text-sm text-gray-500">Timezone</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Categories -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Business Profile -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">Business Profile</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Configure your business information, contact details, and legal documents</p>
                        <ul class="space-y-2 text-sm mb-4">
                            <li>• Business name and address</li>
                            <li>• Contact information</li>
                            <li>• GSTIN and PAN details</li>
                            <li>• Business logo upload</li>
                        </ul>
                        <div class="space-y-2 text-xs text-gray-500">
                            <div><strong>Current:</strong> <?php echo e($settings['business_name']); ?></div>
                            <div><strong>GSTIN:</strong> <?php echo e($settings['gstin'] ?: 'Not set'); ?></div>
                        </div>
                        <div class="mt-4">
                            <a href="<?php echo e(route('settings.business-profile')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full block text-center">
                                Configure Business
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Tax Settings -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">Tax Settings</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Configure GST rates, HSN codes, and tax calculation preferences</p>
                        <ul class="space-y-2 text-sm mb-4">
                            <li>• CGST, SGST, IGST rates</li>
                            <li>• HSN codes for products</li>
                            <li>• Tax inclusive pricing</li>
                            <li>• Making charges HSN</li>
                        </ul>
                        <div class="space-y-2 text-xs text-gray-500">
                            <div><strong>CGST:</strong> <?php echo e($settings['cgst_rate']); ?>%</div>
                            <div><strong>SGST:</strong> <?php echo e($settings['sgst_rate']); ?>%</div>
                        </div>
                        <div class="mt-4">
                            <a href="<?php echo e(route('settings.tax-settings')); ?>" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded w-full block text-center">
                                Configure Tax
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Print Settings -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-purple-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">Print Settings</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Customize invoice templates, paper sizes, and printing preferences</p>
                        <ul class="space-y-2 text-sm mb-4">
                            <li>• Invoice templates</li>
                            <li>• Paper size configuration</li>
                            <li>• Auto-print options</li>
                            <li>• Footer text and logos</li>
                        </ul>
                        <div class="space-y-2 text-xs text-gray-500">
                            <div><strong>Template:</strong> <?php echo e(ucfirst($settings['invoice_template'])); ?></div>
                            <div><strong>Paper:</strong> <?php echo e($settings['receipt_paper_size']); ?></div>
                        </div>
                        <div class="mt-4">
                            <a href="<?php echo e(route('settings.print-settings')); ?>" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded w-full block text-center">
                                Configure Print
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Barcode Settings -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-yellow-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">Barcode Settings</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Configure barcode formats, label templates, and generation preferences</p>
                        <ul class="space-y-2 text-sm mb-4">
                            <li>• Barcode format and size</li>
                            <li>• Label templates</li>
                            <li>• Auto-generation settings</li>
                            <li>• Label content options</li>
                        </ul>
                        <div class="space-y-2 text-xs text-gray-500">
                            <div><strong>Format:</strong> <?php echo e($settings['barcode_format']); ?></div>
                            <div><strong>Template:</strong> <?php echo e(ucfirst($settings['label_template'])); ?></div>
                        </div>
                        <div class="mt-4">
                            <a href="<?php echo e(route('settings.barcode-settings')); ?>" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded w-full block text-center">
                                Configure Barcode
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-red-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">System Settings</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Configure system-wide preferences, formats, and operational settings</p>
                        <ul class="space-y-2 text-sm mb-4">
                            <li>• Currency and formatting</li>
                            <li>• Date and time formats</li>
                            <li>• Language preferences</li>
                            <li>• Backup and alerts</li>
                        </ul>
                        <div class="space-y-2 text-xs text-gray-500">
                            <div><strong>Currency:</strong> <?php echo e($settings['currency_symbol']); ?></div>
                            <div><strong>Date Format:</strong> <?php echo e($settings['date_format']); ?></div>
                        </div>
                        <div class="mt-4">
                            <a href="<?php echo e(route('settings.system-settings')); ?>" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded w-full block text-center">
                                Configure System
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="h-8 w-8 text-indigo-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Common administrative tasks and system maintenance</p>
                        <div class="space-y-3">
                            <button class="w-full bg-indigo-50 hover:bg-indigo-100 text-indigo-700 font-medium py-2 px-4 rounded text-sm">
                                Clear System Cache
                            </button>
                            <button class="w-full bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2 px-4 rounded text-sm">
                                Backup Database
                            </button>
                            <button class="w-full bg-yellow-50 hover:bg-yellow-100 text-yellow-700 font-medium py-2 px-4 rounded text-sm">
                                Export Settings
                            </button>
                            <button class="w-full bg-red-50 hover:bg-red-100 text-red-700 font-medium py-2 px-4 rounded text-sm">
                                Reset to Defaults
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">System Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Application</h4>
                            <dl class="space-y-1 text-sm">
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Version:</dt>
                                    <dd class="text-gray-900">1.0.0</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Environment:</dt>
                                    <dd class="text-gray-900"><?php echo e(app()->environment()); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Debug Mode:</dt>
                                    <dd class="text-gray-900"><?php echo e(config('app.debug') ? 'Enabled' : 'Disabled'); ?></dd>
                                </div>
                            </dl>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Server</h4>
                            <dl class="space-y-1 text-sm">
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">PHP Version:</dt>
                                    <dd class="text-gray-900"><?php echo e(PHP_VERSION); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Laravel:</dt>
                                    <dd class="text-gray-900"><?php echo e(app()->version()); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Timezone:</dt>
                                    <dd class="text-gray-900"><?php echo e(config('app.timezone')); ?></dd>
                                </div>
                            </dl>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Storage</h4>
                            <dl class="space-y-1 text-sm">
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Disk Space:</dt>
                                    <dd class="text-gray-900">Available</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Cache:</dt>
                                    <dd class="text-gray-900"><?php echo e(config('cache.default')); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Session:</dt>
                                    <dd class="text-gray-900"><?php echo e(config('session.driver')); ?></dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\settings\index.blade.php ENDPATH**/ ?>