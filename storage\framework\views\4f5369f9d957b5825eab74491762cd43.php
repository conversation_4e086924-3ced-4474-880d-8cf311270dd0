<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Jewelry Price Calculator')); ?>

            </h2>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('metal-rates.index')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Metal Rates
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Current Rates Display -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Metal Rates</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <?php $__empty_1 = true; $__currentLoopData = $currentRates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $metalType => $purities): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 rounded-lg border border-yellow-200">
                                <h4 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e($metalType); ?></h4>
                                <?php $__currentLoopData = $purities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purity => $rate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-gray-700"><?php echo e($purity); ?></span>
                                        <div class="text-right">
                                            <div class="text-lg font-bold text-gray-900">₹<?php echo e(number_format($rate->rate_per_gram, 2)); ?>/g</div>
                                            <div class="text-xs text-gray-500">₹<?php echo e(number_format($rate->rate_per_10_gram, 2)); ?>/10g</div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="col-span-3 text-center text-gray-500 py-8">
                                No active rates found. Please add some metal rates first.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Calculator Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-6">Price Calculator</h3>
                        
                        <form id="calculatorForm" class="space-y-6">
                            <!-- Metal Selection -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="metal_type" class="block text-sm font-medium text-gray-700">Metal Type *</label>
                                    <select id="metal_type" name="metal_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Metal</option>
                                        <option value="Gold">Gold</option>
                                        <option value="Silver">Silver</option>
                                        <option value="Platinum">Platinum</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="purity" class="block text-sm font-medium text-gray-700">Purity *</label>
                                    <select id="purity" name="purity" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Purity</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Weight Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="gross_weight" class="block text-sm font-medium text-gray-700">Gross Weight (grams) *</label>
                                    <input type="number" id="gross_weight" name="gross_weight" step="0.001" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>
                                <div>
                                    <label for="stone_weight" class="block text-sm font-medium text-gray-700">Stone Weight (grams)</label>
                                    <input type="number" id="stone_weight" name="stone_weight" step="0.001" min="0" value="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>
                            </div>

                            <!-- Rate and Wastage -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="custom_rate" class="block text-sm font-medium text-gray-700">Custom Rate (₹/gram)</label>
                                    <input type="number" id="custom_rate" name="custom_rate" step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <p class="mt-1 text-xs text-gray-500" id="current_rate_display">Leave blank to use current rate</p>
                                </div>
                                <div>
                                    <label for="wastage_percentage" class="block text-sm font-medium text-gray-700">Wastage (%)</label>
                                    <input type="number" id="wastage_percentage" name="wastage_percentage" step="0.01" min="0" max="100" value="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>
                            </div>

                            <!-- Charges -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="making_charges" class="block text-sm font-medium text-gray-700">Making Charges (₹)</label>
                                    <input type="number" id="making_charges" name="making_charges" step="0.01" min="0" value="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>
                                <div>
                                    <label for="stone_charges" class="block text-sm font-medium text-gray-700">Stone Charges (₹)</label>
                                    <input type="number" id="stone_charges" name="stone_charges" step="0.01" min="0" value="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>
                            </div>

                            <!-- Calculate Button -->
                            <div>
                                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-lg">
                                    Calculate Price
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Results Display -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-6">Calculation Results</h3>
                        
                        <div id="calculationResults" class="hidden">
                            <!-- Weight Breakdown -->
                            <div class="mb-6">
                                <h4 class="text-md font-semibold text-gray-800 mb-3">Weight Breakdown</h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Gross Weight:</span>
                                        <span id="result_gross_weight" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Stone Weight:</span>
                                        <span id="result_stone_weight" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2">
                                        <span class="text-gray-600">Net Weight:</span>
                                        <span id="result_net_weight" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Wastage Weight:</span>
                                        <span id="result_wastage_weight" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2 font-semibold">
                                        <span class="text-gray-800">Total Weight:</span>
                                        <span id="result_total_weight" class="text-gray-800">-</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Price Breakdown -->
                            <div class="mb-6">
                                <h4 class="text-md font-semibold text-gray-800 mb-3">Price Breakdown</h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Rate per Gram:</span>
                                        <span id="result_rate_per_gram" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Metal Value:</span>
                                        <span id="result_metal_value" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Making Charges:</span>
                                        <span id="result_making_charges" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Stone Charges:</span>
                                        <span id="result_stone_charges" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2">
                                        <span class="text-gray-600">Subtotal:</span>
                                        <span id="result_subtotal" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">GST (3%):</span>
                                        <span id="result_gst_amount" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2 text-lg font-bold">
                                        <span class="text-gray-800">Total Amount:</span>
                                        <span id="result_total_amount" class="text-green-600">-</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="space-y-2">
                                <button id="createEstimate" class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Create Estimate
                                </button>
                                <button id="addToSale" class="w-full bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                    Add to Sale
                                </button>
                            </div>
                        </div>

                        <div id="noResults" class="text-center text-gray-500 py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                            <p class="mt-2">Enter details above and click Calculate to see results</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const currentRates = <?php echo json_encode($currentRates, 15, 512) ?>;
            const metalTypeSelect = document.getElementById('metal_type');
            const puritySelect = document.getElementById('purity');
            const customRateInput = document.getElementById('custom_rate');
            const currentRateDisplay = document.getElementById('current_rate_display');
            const calculatorForm = document.getElementById('calculatorForm');
            const calculationResults = document.getElementById('calculationResults');
            const noResults = document.getElementById('noResults');

            // Purity options based on metal type
            const purityOptions = {
                'Gold': ['22K', '18K', '14K'],
                'Silver': ['925', '999'],
                'Platinum': ['950', '900']
            };

            // Update purity options when metal type changes
            metalTypeSelect.addEventListener('change', function() {
                const selectedMetal = this.value;
                puritySelect.innerHTML = '<option value="">Select Purity</option>';
                
                if (selectedMetal && purityOptions[selectedMetal]) {
                    purityOptions[selectedMetal].forEach(purity => {
                        const option = document.createElement('option');
                        option.value = purity;
                        option.textContent = purity;
                        puritySelect.appendChild(option);
                    });
                }
                
                updateCurrentRateDisplay();
            });

            // Update current rate display when purity changes
            puritySelect.addEventListener('change', updateCurrentRateDisplay);

            function updateCurrentRateDisplay() {
                const metalType = metalTypeSelect.value;
                const purity = puritySelect.value;
                
                if (metalType && purity && currentRates[metalType] && currentRates[metalType][purity]) {
                    const rate = currentRates[metalType][purity];
                    currentRateDisplay.textContent = `Current rate: ₹${rate.rate_per_gram}/gram`;
                    if (!customRateInput.value) {
                        customRateInput.placeholder = rate.rate_per_gram;
                    }
                } else {
                    currentRateDisplay.textContent = 'Leave blank to use current rate';
                    customRateInput.placeholder = '';
                }
            }

            // Handle form submission
            calculatorForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const data = Object.fromEntries(formData.entries());

                fetch('<?php echo e(route("calculator.calculate")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        displayResults(result.calculations);
                    } else {
                        alert(result.error || 'An error occurred during calculation');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred during calculation');
                });
            });

            function displayResults(calc) {
                // Weight breakdown
                document.getElementById('result_gross_weight').textContent = `${calc.gross_weight}g`;
                document.getElementById('result_stone_weight').textContent = `${calc.stone_weight}g`;
                document.getElementById('result_net_weight').textContent = `${calc.net_weight}g`;
                document.getElementById('result_wastage_weight').textContent = `${calc.wastage_weight}g (${calc.wastage_percentage}%)`;
                document.getElementById('result_total_weight').textContent = `${calc.total_weight}g`;

                // Price breakdown
                document.getElementById('result_rate_per_gram').textContent = `₹${calc.rate_per_gram}`;
                document.getElementById('result_metal_value').textContent = `₹${calc.metal_value.toLocaleString()}`;
                document.getElementById('result_making_charges').textContent = `₹${calc.making_charges.toLocaleString()}`;
                document.getElementById('result_stone_charges').textContent = `₹${calc.stone_charges.toLocaleString()}`;
                document.getElementById('result_subtotal').textContent = `₹${calc.subtotal.toLocaleString()}`;
                document.getElementById('result_gst_amount').textContent = `₹${calc.gst_amount.toLocaleString()}`;
                document.getElementById('result_total_amount').textContent = `₹${calc.total_amount.toLocaleString()}`;

                // Show results
                noResults.classList.add('hidden');
                calculationResults.classList.remove('hidden');
            }

            // Quick action buttons
            document.getElementById('createEstimate').addEventListener('click', function() {
                // Redirect to estimate creation with pre-filled data
                const params = new URLSearchParams();
                const formData = new FormData(calculatorForm);
                for (let [key, value] of formData.entries()) {
                    if (value) params.append(key, value);
                }
                window.location.href = `/estimates/create?${params.toString()}`;
            });

            document.getElementById('addToSale').addEventListener('click', function() {
                // Redirect to sales creation with pre-filled data
                const params = new URLSearchParams();
                const formData = new FormData(calculatorForm);
                for (let [key, value] of formData.entries()) {
                    if (value) params.append(key, value);
                }
                window.location.href = `/sales/create?${params.toString()}`;
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\metal-rates\calculator.blade.php ENDPATH**/ ?>