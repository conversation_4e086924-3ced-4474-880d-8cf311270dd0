<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Saving Scheme Details')); ?> - <?php echo e($savingScheme->scheme_number); ?>

            </h2>
            <div class="flex space-x-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage_scheme_payments')): ?>
                    <?php if($savingScheme->status === 'active'): ?>
                        <a href="<?php echo e(route('saving-schemes.add-payment', $savingScheme)); ?>" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            Add Payment
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_saving_scheme')): ?>
                    <?php if($savingScheme->status === 'active'): ?>
                        <a href="<?php echo e(route('saving-schemes.edit', $savingScheme)); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Edit Scheme
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if($savingScheme->status === 'matured'): ?>
                    <a href="<?php echo e(route('saving-schemes.certificate', $savingScheme)); ?>" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        Certificate
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('saving-schemes.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Schemes
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <!-- Scheme Overview -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Scheme Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Scheme Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Scheme Number</dt>
                                    <dd class="text-sm text-gray-900 font-mono"><?php echo e($savingScheme->scheme_number); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Scheme Name</dt>
                                    <dd class="text-sm text-gray-900 font-medium"><?php echo e($savingScheme->scheme_name); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Monthly Amount</dt>
                                    <dd class="text-sm text-gray-900">₹<?php echo e(number_format($savingScheme->monthly_amount, 2)); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Duration</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($savingScheme->duration_months); ?> months</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php echo e($savingScheme->status === 'active' ? 'bg-blue-100 text-blue-800' : 
                                               ($savingScheme->status === 'matured' ? 'bg-green-100 text-green-800' : 
                                               ($savingScheme->status === 'closed' ? 'bg-gray-100 text-gray-800' : 'bg-red-100 text-red-800'))); ?>">
                                            <?php echo e(ucfirst($savingScheme->status)); ?>

                                        </span>
                                        <?php if($savingScheme->is_overdue): ?>
                                            <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Overdue
                                            </span>
                                        <?php endif; ?>
                                    </dd>
                                </div>
                                <?php if($savingScheme->auto_debit): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Auto Debit</dt>
                                        <dd class="text-sm text-blue-600">Enabled</dd>
                                    </div>
                                <?php endif; ?>
                            </dl>
                        </div>

                        <!-- Customer Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($savingScheme->customer->name); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Mobile</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($savingScheme->customer->mobile); ?></dd>
                                </div>
                                <?php if($savingScheme->customer->email): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="text-sm text-gray-900"><?php echo e($savingScheme->customer->email); ?></dd>
                                    </div>
                                <?php endif; ?>
                                <?php if($savingScheme->customer->address): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="text-sm text-gray-900"><?php echo e($savingScheme->customer->full_address); ?></dd>
                                    </div>
                                <?php endif; ?>
                            </dl>
                        </div>
                    </div>

                    <?php if($savingScheme->notes): ?>
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                            <p class="text-sm text-gray-700"><?php echo e($savingScheme->notes); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Progress and Dates -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Progress Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Progress Information</h3>
                        
                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>Progress</span>
                                <span><?php echo e(number_format($savingScheme->completion_percentage, 1)); ?>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-blue-600 h-3 rounded-full" style="width: <?php echo e($savingScheme->completion_percentage); ?>%"></div>
                            </div>
                        </div>

                        <dl class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Paid</dt>
                                <dd class="text-lg font-bold text-green-600">₹<?php echo e(number_format($savingScheme->total_paid, 2)); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Expected Total</dt>
                                <dd class="text-sm text-gray-900">₹<?php echo e(number_format($savingScheme->expected_total, 2)); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Pending Amount</dt>
                                <dd class="text-sm <?php echo e($savingScheme->pending_amount > 0 ? 'text-red-600' : 'text-green-600'); ?>">
                                    ₹<?php echo e(number_format($savingScheme->pending_amount, 2)); ?>

                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Bonus Amount</dt>
                                <dd class="text-sm text-blue-600">₹<?php echo e(number_format($savingScheme->bonus_amount, 2)); ?></dd>
                            </div>
                            <div class="border-t pt-2">
                                <dt class="text-sm font-medium text-gray-500">Total Value</dt>
                                <dd class="text-lg font-bold text-blue-600">₹<?php echo e(number_format($savingScheme->total_value, 2)); ?></dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Important Dates -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Important Dates</h3>
                        <dl class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Start Date</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($savingScheme->start_date->format('d M, Y')); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Maturity Date</dt>
                                <dd class="text-sm <?php echo e($savingScheme->is_matured ? 'text-green-600' : 'text-gray-900'); ?>">
                                    <?php echo e($savingScheme->maturity_date->format('d M, Y')); ?>

                                </dd>
                            </div>
                            <?php if($savingScheme->next_due_date): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Next Due Date</dt>
                                    <dd class="text-sm <?php echo e($savingScheme->next_due_date < today() ? 'text-red-600 font-semibold' : 'text-blue-600'); ?>">
                                        <?php echo e($savingScheme->next_due_date->format('d M, Y')); ?>

                                    </dd>
                                </div>
                            <?php endif; ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Months Completed</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($savingScheme->months_completed); ?> / <?php echo e($savingScheme->duration_months); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created By</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($savingScheme->createdBy->name); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created On</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($savingScheme->created_at->format('d M, Y h:i A')); ?></dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- Payment Schedule -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Schedule</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expected</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__currentLoopData = $paymentSchedule; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="<?php echo e($schedule['status'] === 'overdue' ? 'bg-red-50' : ''); ?>">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            Month <?php echo e($schedule['month']); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo e($schedule['due_date']->format('d M, Y')); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ₹<?php echo e(number_format($schedule['expected_amount'], 2)); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <?php if($schedule['paid_amount'] > 0): ?>
                                                <span class="text-green-600 font-medium">₹<?php echo e(number_format($schedule['paid_amount'], 2)); ?></span>
                                                <?php if($schedule['late_fee'] > 0): ?>
                                                    <div class="text-xs text-red-600">Late Fee: ₹<?php echo e(number_format($schedule['late_fee'], 2)); ?></div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-gray-400">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php if($schedule['payment_date']): ?>
                                                <?php echo e($schedule['payment_date']->format('d M, Y')); ?>

                                                <?php if($schedule['is_late']): ?>
                                                    <div class="text-xs text-red-600">Late Payment</div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-gray-400">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                <?php echo e($schedule['status'] === 'paid' ? 'bg-green-100 text-green-800' : 
                                                   ($schedule['status'] === 'partial' ? 'bg-yellow-100 text-yellow-800' : 
                                                   ($schedule['status'] === 'overdue' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'))); ?>">
                                                <?php echo e(ucfirst($schedule['status'])); ?>

                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            <?php if($savingScheme->payments->count() > 0): ?>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Payment History</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $savingScheme->payments->sortByDesc('payment_date'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($payment->payment_date->format('d M, Y')); ?>

                                                <?php if($payment->is_late): ?>
                                                    <div class="text-xs text-red-600">Late Payment (<?php echo e($payment->days_late); ?> days)</div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div class="text-green-600 font-medium">₹<?php echo e(number_format($payment->amount, 2)); ?></div>
                                                <?php if($payment->late_fee > 0): ?>
                                                    <div class="text-xs text-red-600">Late Fee: ₹<?php echo e(number_format($payment->late_fee, 2)); ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $payment->payment_method))); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($payment->transaction_reference ?: '-'); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($payment->createdBy->name); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\saving-schemes\show.blade.php ENDPATH**/ ?>