<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Metal Rate Details') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('metal-rates.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Metal Rates
                </a>
                @can('edit_metal_rate')
                    <a href="{{ route('metal-rates.edit', $metalRate) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Rate
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Metal Rate Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Rate Information</h3>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Metal Type:</span>
                                <span class="text-sm text-gray-900 font-semibold">{{ $metalRate->metal_type }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Purity:</span>
                                <span class="text-sm text-gray-900 font-semibold">{{ $metalRate->purity }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Rate per Gram:</span>
                                <span class="text-lg text-gray-900 font-bold">₹{{ number_format($metalRate->rate_per_gram, 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Rate per 10 Grams:</span>
                                <span class="text-lg text-gray-900 font-bold">₹{{ number_format($metalRate->rate_per_10_gram, 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Effective Date:</span>
                                <span class="text-sm text-gray-900">{{ $metalRate->effective_date->format('d M Y') }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Status:</span>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $metalRate->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $metalRate->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Created By:</span>
                                <span class="text-sm text-gray-900">{{ $metalRate->createdBy->name ?? 'N/A' }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Created Date:</span>
                                <span class="text-sm text-gray-900">{{ $metalRate->created_at->format('d M Y, h:i A') }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Last Updated:</span>
                                <span class="text-sm text-gray-900">{{ $metalRate->updated_at->format('d M Y, h:i A') }}</span>
                            </div>

                            <!-- Quick Calculator -->
                            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Calculator</h4>
                                <div class="flex items-center space-x-2">
                                    <input type="number" id="weight" placeholder="Weight (grams)" step="0.01" min="0"
                                           class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <span class="text-sm text-gray-500">=</span>
                                    <span id="calculated-amount" class="text-lg font-bold text-gray-900">₹0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rate History -->
            @if($rateHistory->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Rate History ({{ $metalRate->metal_type }} - {{ $metalRate->purity }})</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Effective Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per Gram</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per 10g</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($rateHistory as $rate)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $rate->effective_date->format('d M Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                ₹{{ number_format($rate->rate_per_gram, 2) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ₹{{ number_format($rate->rate_per_10_gram, 2) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $rate->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    {{ $rate->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $rate->created_at->format('d M Y') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const weightInput = document.getElementById('weight');
            const calculatedAmount = document.getElementById('calculated-amount');
            const ratePerGram = {{ $metalRate->rate_per_gram }};

            weightInput.addEventListener('input', function() {
                const weight = parseFloat(this.value) || 0;
                const amount = weight * ratePerGram;
                calculatedAmount.textContent = '₹' + amount.toFixed(2);
            });
        });
    </script>
</x-app-layout>
