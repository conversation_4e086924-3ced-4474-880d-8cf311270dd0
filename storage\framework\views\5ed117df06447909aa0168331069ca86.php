<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Old Gold Purchase Details')); ?> - <?php echo e($oldGoldPurchase->purchase_number); ?>

            </h2>
            <div class="flex space-x-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_old_gold_purchase')): ?>
                    <a href="<?php echo e(route('old-gold-purchases.edit', $oldGoldPurchase)); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Purchase
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('old-gold-purchases.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Purchases
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Purchase Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Purchase Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Purchase Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Purchase Number</dt>
                                    <dd class="text-sm text-gray-900 font-mono"><?php echo e($oldGoldPurchase->purchase_number); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Purchase Date</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->created_at->format('d M, Y h:i A')); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created By</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->createdBy->name); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php echo e($oldGoldPurchase->status === 'purchased' ? 'bg-blue-100 text-blue-800' : 
                                               ($oldGoldPurchase->status === 'converted_to_voucher' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800')); ?>">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $oldGoldPurchase->status))); ?>

                                        </span>
                                    </dd>
                                </div>
                                <?php if($oldGoldPurchase->usedInSale): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Used in Sale</dt>
                                        <dd class="text-sm text-blue-600">
                                            <a href="<?php echo e(route('sales.show', $oldGoldPurchase->usedInSale)); ?>" class="hover:text-blue-800">
                                                <?php echo e($oldGoldPurchase->usedInSale->invoice_number); ?>

                                            </a>
                                        </dd>
                                    </div>
                                <?php endif; ?>
                            </dl>
                        </div>

                        <!-- Customer Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->customer->name); ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Mobile</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->customer->mobile); ?></dd>
                                </div>
                                <?php if($oldGoldPurchase->customer->email): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->customer->email); ?></dd>
                                    </div>
                                <?php endif; ?>
                                <?php if($oldGoldPurchase->customer->address): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->customer->full_address); ?></dd>
                                    </div>
                                <?php endif; ?>
                            </dl>
                        </div>
                    </div>

                    <?php if($oldGoldPurchase->notes): ?>
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                            <p class="text-sm text-gray-700"><?php echo e($oldGoldPurchase->notes); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Metal and Weight Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Metal Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                        <dl class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Metal Type</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->metal_type); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Purity</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->purity); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Rate per Gram</dt>
                                <dd class="text-sm text-gray-900">₹<?php echo e(number_format($oldGoldPurchase->rate_per_gram, 2)); ?></dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Weight Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Weight Breakdown</h3>
                        <dl class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Gross Weight</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->gross_weight); ?> grams</dd>
                            </div>
                            <?php if($oldGoldPurchase->stone_weight > 0): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Stone Weight</dt>
                                    <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->stone_weight); ?> grams</dd>
                                </div>
                            <?php endif; ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Net Weight</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($oldGoldPurchase->net_weight); ?> grams</dd>
                            </div>
                            <?php if($oldGoldPurchase->melting_loss_percentage > 0): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Melting Loss</dt>
                                    <dd class="text-sm text-red-600"><?php echo e($oldGoldPurchase->melting_loss_percentage); ?>% (<?php echo e($oldGoldPurchase->melting_loss_weight); ?> grams)</dd>
                                </div>
                            <?php endif; ?>
                            <div class="border-t pt-2">
                                <dt class="text-sm font-medium text-gray-500">Final Weight</dt>
                                <dd class="text-base font-bold text-gray-900"><?php echo e($oldGoldPurchase->final_weight); ?> grams</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-gray-500">Total Amount</div>
                            <div class="text-2xl font-bold text-gray-900">₹<?php echo e(number_format($oldGoldPurchase->total_amount, 2)); ?></div>
                            <div class="text-xs text-gray-500 mt-1"><?php echo e($oldGoldPurchase->final_weight); ?>g × ₹<?php echo e(number_format($oldGoldPurchase->rate_per_gram, 2)); ?>/g</div>
                        </div>

                        <?php if($oldGoldPurchase->cash_paid > 0): ?>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="text-sm font-medium text-gray-500">Cash Paid</div>
                                <div class="text-2xl font-bold text-blue-600">₹<?php echo e(number_format($oldGoldPurchase->cash_paid, 2)); ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if($oldGoldPurchase->voucher_amount > 0): ?>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-sm font-medium text-gray-500">
                                    <?php if($oldGoldPurchase->status === 'used_in_exchange'): ?>
                                        Voucher Amount (Used)
                                    <?php else: ?>
                                        Available Voucher
                                    <?php endif; ?>
                                </div>
                                <div class="text-2xl font-bold <?php echo e($oldGoldPurchase->status === 'used_in_exchange' ? 'text-gray-600' : 'text-green-600'); ?>">
                                    ₹<?php echo e(number_format($oldGoldPurchase->voucher_amount, 2)); ?>

                                </div>
                                <?php if($oldGoldPurchase->status !== 'used_in_exchange'): ?>
                                    <div class="text-xs text-green-600 mt-1">Can be used in future purchases</div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Calculation Breakdown -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Calculation Breakdown</h4>
                        <div class="text-sm space-y-1">
                            <div class="flex justify-between">
                                <span>Gross Weight:</span>
                                <span><?php echo e($oldGoldPurchase->gross_weight); ?> grams</span>
                            </div>
                            <?php if($oldGoldPurchase->stone_weight > 0): ?>
                                <div class="flex justify-between text-red-600">
                                    <span>Less: Stone Weight:</span>
                                    <span><?php echo e($oldGoldPurchase->stone_weight); ?> grams</span>
                                </div>
                            <?php endif; ?>
                            <div class="flex justify-between border-t pt-1">
                                <span>Net Weight:</span>
                                <span><?php echo e($oldGoldPurchase->net_weight); ?> grams</span>
                            </div>
                            <?php if($oldGoldPurchase->melting_loss_percentage > 0): ?>
                                <div class="flex justify-between text-red-600">
                                    <span>Less: Melting Loss (<?php echo e($oldGoldPurchase->melting_loss_percentage); ?>%):</span>
                                    <span><?php echo e($oldGoldPurchase->melting_loss_weight); ?> grams</span>
                                </div>
                            <?php endif; ?>
                            <div class="flex justify-between border-t pt-1 font-medium">
                                <span>Final Weight:</span>
                                <span><?php echo e($oldGoldPurchase->final_weight); ?> grams</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Rate per Gram:</span>
                                <span>₹<?php echo e(number_format($oldGoldPurchase->rate_per_gram, 2)); ?></span>
                            </div>
                            <div class="flex justify-between border-t pt-1 font-bold text-lg">
                                <span>Total Amount:</span>
                                <span>₹<?php echo e(number_format($oldGoldPurchase->total_amount, 2)); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\old-gold-purchases\show.blade.php ENDPATH**/ ?>