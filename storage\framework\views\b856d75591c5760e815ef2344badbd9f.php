<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Create New Saving Scheme')); ?>

            </h2>
            <a href="<?php echo e(route('saving-schemes.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Schemes
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="<?php echo e(route('saving-schemes.store')); ?>" id="schemeForm">
                        <?php echo csrf_field(); ?>

                        <!-- Customer Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer *</label>
                                    <select name="customer_id" id="customer_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Customer</option>
                                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($customer->id); ?>" <?php echo e(old('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                                <?php echo e($customer->name); ?> - <?php echo e($customer->mobile); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['customer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Scheme Details -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Scheme Details</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="scheme_name" class="block text-sm font-medium text-gray-700">Scheme Name *</label>
                                    <select name="scheme_name" id="scheme_name" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Scheme</option>
                                        <option value="Gold Saving Scheme" <?php echo e(old('scheme_name') === 'Gold Saving Scheme' ? 'selected' : ''); ?>>Gold Saving Scheme</option>
                                        <option value="Silver Saving Scheme" <?php echo e(old('scheme_name') === 'Silver Saving Scheme' ? 'selected' : ''); ?>>Silver Saving Scheme</option>
                                        <option value="Diamond Saving Scheme" <?php echo e(old('scheme_name') === 'Diamond Saving Scheme' ? 'selected' : ''); ?>>Diamond Saving Scheme</option>
                                        <option value="Wedding Collection Scheme" <?php echo e(old('scheme_name') === 'Wedding Collection Scheme' ? 'selected' : ''); ?>>Wedding Collection Scheme</option>
                                        <option value="Festival Special Scheme" <?php echo e(old('scheme_name') === 'Festival Special Scheme' ? 'selected' : ''); ?>>Festival Special Scheme</option>
                                    </select>
                                    <?php $__errorArgs = ['scheme_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    <label for="monthly_amount" class="block text-sm font-medium text-gray-700">Monthly Amount (₹) *</label>
                                    <input type="number" name="monthly_amount" id="monthly_amount" value="<?php echo e(old('monthly_amount')); ?>" 
                                           step="100" min="100" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <?php $__errorArgs = ['monthly_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Duration and Dates -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Duration & Dates</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="duration_months" class="block text-sm font-medium text-gray-700">Duration (Months) *</label>
                                    <select name="duration_months" id="duration_months" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Duration</option>
                                        <option value="6" <?php echo e(old('duration_months') == '6' ? 'selected' : ''); ?>>6 Months</option>
                                        <option value="12" <?php echo e(old('duration_months') == '12' ? 'selected' : ''); ?>>12 Months</option>
                                        <option value="18" <?php echo e(old('duration_months') == '18' ? 'selected' : ''); ?>>18 Months</option>
                                        <option value="24" <?php echo e(old('duration_months') == '24' ? 'selected' : ''); ?>>24 Months</option>
                                        <option value="36" <?php echo e(old('duration_months') == '36' ? 'selected' : ''); ?>>36 Months</option>
                                        <option value="48" <?php echo e(old('duration_months') == '48' ? 'selected' : ''); ?>>48 Months</option>
                                        <option value="60" <?php echo e(old('duration_months') == '60' ? 'selected' : ''); ?>>60 Months</option>
                                    </select>
                                    <?php $__errorArgs = ['duration_months'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date *</label>
                                    <input type="date" name="start_date" id="start_date" value="<?php echo e(old('start_date', today()->format('Y-m-d'))); ?>" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    <label for="maturity_date" class="block text-sm font-medium text-gray-700">Maturity Date</label>
                                    <input type="date" id="maturity_date" readonly
                                           class="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm">
                                    <p class="mt-1 text-xs text-gray-500">Automatically calculated</p>
                                </div>
                            </div>
                        </div>

                        <!-- Bonus and Options -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Bonus & Options</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="bonus_amount" class="block text-sm font-medium text-gray-700">Bonus Amount (₹)</label>
                                    <input type="number" name="bonus_amount" id="bonus_amount" value="<?php echo e(old('bonus_amount', 0)); ?>" 
                                           step="100" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <p class="mt-1 text-xs text-gray-500">Additional bonus amount on maturity</p>
                                    <?php $__errorArgs = ['bonus_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="flex items-center mt-6">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="auto_debit" value="1" <?php echo e(old('auto_debit') ? 'checked' : ''); ?>

                                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm text-gray-700">Enable Auto Debit</span>
                                    </label>
                                    <div class="ml-2">
                                        <div class="text-xs text-gray-500">
                                            Automatically deduct monthly amount
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Calculation Summary -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Calculation Summary</h3>
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Expected Total</label>
                                        <div class="text-lg font-bold text-gray-900" id="expectedTotalDisplay">₹0</div>
                                        <div class="text-xs text-gray-500">Monthly × Duration</div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Bonus Amount</label>
                                        <div class="text-lg font-bold text-green-600" id="bonusAmountDisplay">₹0</div>
                                        <div class="text-xs text-gray-500">Additional benefit</div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Total Value</label>
                                        <div class="text-xl font-bold text-blue-600" id="totalValueDisplay">₹0</div>
                                        <div class="text-xs text-gray-500">Expected + Bonus</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-8">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"><?php echo e(old('notes')); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-4">
                            <a href="<?php echo e(route('saving-schemes.index')); ?>" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Saving Scheme
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const monthlyAmountInput = document.getElementById('monthly_amount');
            const durationSelect = document.getElementById('duration_months');
            const startDateInput = document.getElementById('start_date');
            const maturityDateInput = document.getElementById('maturity_date');
            const bonusAmountInput = document.getElementById('bonus_amount');
            
            const expectedTotalDisplay = document.getElementById('expectedTotalDisplay');
            const bonusAmountDisplay = document.getElementById('bonusAmountDisplay');
            const totalValueDisplay = document.getElementById('totalValueDisplay');

            function updateCalculations() {
                const monthlyAmount = parseFloat(monthlyAmountInput.value) || 0;
                const duration = parseInt(durationSelect.value) || 0;
                const bonusAmount = parseFloat(bonusAmountInput.value) || 0;

                const expectedTotal = monthlyAmount * duration;
                const totalValue = expectedTotal + bonusAmount;

                expectedTotalDisplay.textContent = `₹${expectedTotal.toLocaleString()}`;
                bonusAmountDisplay.textContent = `₹${bonusAmount.toLocaleString()}`;
                totalValueDisplay.textContent = `₹${totalValue.toLocaleString()}`;
            }

            function updateMaturityDate() {
                const startDate = startDateInput.value;
                const duration = parseInt(durationSelect.value) || 0;

                if (startDate && duration) {
                    const start = new Date(startDate);
                    const maturity = new Date(start);
                    maturity.setMonth(maturity.getMonth() + duration);
                    
                    maturityDateInput.value = maturity.toISOString().split('T')[0];
                }
            }

            // Event listeners
            monthlyAmountInput.addEventListener('input', updateCalculations);
            durationSelect.addEventListener('change', function() {
                updateCalculations();
                updateMaturityDate();
            });
            startDateInput.addEventListener('change', updateMaturityDate);
            bonusAmountInput.addEventListener('input', updateCalculations);

            // Initialize calculations
            updateCalculations();
            updateMaturityDate();
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views\saving-schemes\create.blade.php ENDPATH**/ ?>